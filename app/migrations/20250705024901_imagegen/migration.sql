-- CreateTable
CREATE TABLE "image_generation" (
    "id" TEXT NOT NULL,
    "imageId" TEXT NOT NULL,
    "originalPrompt" TEXT NOT NULL,
    "modelUsed" TEXT NOT NULL,
    "aspectRatio" TEXT NOT NULL,
    "quality" TEXT NOT NULL,
    "referenceImages" JSONB,
    "brandElements" JSONB,
    "adType" TEXT,
    "generationParams" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "image_generation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "image_generation_imageId_key" ON "image_generation"("imageId");

-- CreateIndex
CREATE INDEX "image_generation_imageId_idx" ON "image_generation"("imageId");

-- CreateIndex
CREATE INDEX "image_generation_createdAt_idx" ON "image_generation"("createdAt");
