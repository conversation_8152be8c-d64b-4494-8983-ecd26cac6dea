<svg width="192" height="192" viewBox="0 0 192 192" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with theme color -->
  <circle cx="96" cy="96" r="96" fill="#9EA581"/>
  
  <!-- Camera icon representing photography -->
  <g transform="translate(48, 48)">
    <!-- Camera body -->
    <rect x="16" y="32" width="64" height="48" rx="8" fill="#F0EFE9" stroke="#566B46" stroke-width="2"/>
    
    <!-- Camera lens -->
    <circle cx="48" cy="56" r="16" fill="#566B46"/>
    <circle cx="48" cy="56" r="10" fill="#F0EFE9"/>
    <circle cx="48" cy="56" r="6" fill="#566B46"/>
    
    <!-- Camera flash -->
    <rect x="20" y="24" width="8" height="8" rx="2" fill="#F0EFE9"/>
    
    <!-- Camera viewfinder -->
    <rect x="32" y="20" width="32" height="12" rx="6" fill="#566B46"/>
    
    <!-- AI sparkle elements -->
    <g fill="#C8A13C">
      <!-- Top right sparkle -->
      <circle cx="72" cy="24" r="2"/>
      <rect x="71" y="20" width="2" height="8" rx="1"/>
      <rect x="68" y="23" width="8" height="2" rx="1"/>
      
      <!-- Bottom left sparkle -->
      <circle cx="24" cy="72" r="1.5"/>
      <rect x="23.5" y="69" width="1" height="6" rx="0.5"/>
      <rect x="21" y="71.5" width="6" height="1" rx="0.5"/>
      
      <!-- Small accent dots -->
      <circle cx="64" cy="36" r="1"/>
      <circle cx="32" cy="64" r="1"/>
    </g>
  </g>
  
  <!-- Subtle gradient overlay -->
  <defs>
    <radialGradient id="overlay" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" stop-color="rgba(255,255,255,0.1)"/>
      <stop offset="100%" stop-color="rgba(0,0,0,0.05)"/>
    </radialGradient>
  </defs>
  <circle cx="96" cy="96" r="96" fill="url(#overlay)"/>
</svg>
