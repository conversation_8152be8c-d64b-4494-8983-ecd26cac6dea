// Domain types for the Audience feature
export type NullableString = string | null | undefined;

// Audience tier type
export type AudienceTier = 'primary' | 'secondary' | 'tertiary';

// Core audience entity interface matching Prisma schema
export interface Audience {
  id: number;
  personaName: NullableString;

  // Demographics
  age: NullableString;
  gender: NullableString;
  maritalStatus: NullableString;
  numberOfChildren: NullableString;
  location: NullableString;
  occupation: NullableString;
  jobTitle: NullableString;
  annualIncome: NullableString;
  educationLevel: NullableString;

  // Goals and Values
  goals: NullableString;
  values: NullableString;
  aspirations: NullableString;
  idealDay: NullableString;

  // Challenges and Pain Points
  challenges: NullableString;
  painPoints: NullableString;
  frustrations: NullableString;
  fears: NullableString;

  // Sources of Information
  books: NullableString;
  magazines: NullableString;
  blogsWebsites: NullableString;
  conferences: NullableString;
  gurus: NullableString;
  otherSources: NullableString;

  // Objections and Roles
  possibleObjections: NullableString;
  roleInPurchaseProcess: NullableString;
  decisionMakingFactors: NullableString;
  budgetConcerns: NullableString;

  // Additional Context
  quote: NullableString;
  bio: NullableString;

  // System fields
  avatarUrl: NullableString;
  tier?: AudienceTier;
  userId: string;
  organizationId: string;
  createdAt?: Date;
  updatedAt?: Date;
}

// Form data interface for audience creation/editing
export interface AudienceFormData {
  personaName: string;

  // Demographics
  age: string;
  gender: string;
  maritalStatus: string;
  numberOfChildren: string;
  location: string;
  occupation: string;
  jobTitle: string;
  annualIncome: string;
  educationLevel: string;

  // Goals and Values
  goals: string;
  values: string;
  aspirations: string;
  idealDay: string;

  // Challenges and Pain Points
  challenges: string;
  painPoints: string;
  frustrations: string;
  fears: string;

  // Sources of Information
  books: string;
  magazines: string;
  blogsWebsites: string;
  conferences: string;
  gurus: string;
  otherSources: string;

  // Objections and Roles
  possibleObjections: string;
  roleInPurchaseProcess: string;
  decisionMakingFactors: string;
  budgetConcerns: string;

  // Additional Context
  quote: string;
  bio: string;
}

// Avatar generation options - compatible with SuperJSON
export interface AvatarGenerationOptions {
  [key: string]: string | undefined;
  // Basic appearance
  gender?: string;
  age?: string;
  ethnicity?: string;
  bodyType?: string;

  // Physical features
  hairColor?: string;
  eyeColor?: string;

  // Style and clothing
  clothing?: string;
  style?: 'realistic' | 'artistic' | 'cartoon';
  pose?: 'standing' | 'sitting' | 'walking' | 'portrait' | 'action';
  shot?: 'full-body' | 'half-body' | 'headshot';
}

// Audience selector component props
export interface AudienceSelectorProps {
  onSelect: (audience: Audience) => void;
  selectedId?: string;
  className?: string;
}

// Import progress tracking - compatible with SuperJSON
export interface ImportProgress {
  [key: string]: string | number;
  progress: number;
  message: string;
  taskId: string;
}

// Audience generation input - compatible with SuperJSON
export interface AudienceGenerationInput {
  [key: string]: string | number | undefined;
  taskId: string;
  organizationId: string;
  brandKitId?: string; // Made optional for async generation
  productId: string;
  numberOfAudiences?: number;
}

// Audience creation input - compatible with SuperJSON
export interface CreateAudienceInput {
  [key: string]: string;
  personaName: string;
  organizationId: string;
}

// Audience update input - compatible with SuperJSON
export interface UpdateAudienceInput extends Partial<AudienceFormData> {
  [key: string]: string | number | undefined;
  id: number;
}

// Avatar generation result - compatible with SuperJSON
export interface AvatarGenerationResult {
  [key: string]: boolean | string | undefined;
  success: boolean;
  avatarUrl?: string;
  error?: string;
}

// Domain events
export interface AudienceCreatedEvent {
  type: 'AUDIENCE_CREATED';
  payload: {
    audienceId: string;
    organizationId: string;
    userId: string;
  };
}

export interface AudienceUpdatedEvent {
  type: 'AUDIENCE_UPDATED';
  payload: {
    audienceId: string;
    changes: Partial<AudienceFormData>;
    userId: string;
  };
}

export interface AvatarGeneratedEvent {
  type: 'AVATAR_GENERATED';
  payload: {
    audienceId: string;
    avatarUrl: string;
    userId: string;
  };
}

export type AudienceDomainEvent = AudienceCreatedEvent | AudienceUpdatedEvent | AvatarGeneratedEvent;

// Repository interfaces
export interface AudienceRepository {
  findById(id: string): Promise<Audience | null>;
  findByOrganization(organizationId: string): Promise<Audience[]>;
  create(data: CreateAudienceInput & { userId: string }): Promise<Audience>;
  update(id: string, data: Partial<AudienceFormData>): Promise<Audience>;
  delete(id: string): Promise<void>;
}

// Service interfaces
export interface AudienceGeneratorService {
  generateAudiences(input: AudienceGenerationInput): Promise<Audience[]>;
}

export interface AvatarGeneratorService {
  generateAvatar(audienceId: string, options: AvatarGenerationOptions): Promise<AvatarGenerationResult>;
}

export interface PersonaAnalyzerService {
  analyzePersona(audience: Audience): Promise<Partial<AudienceFormData>>;
}
