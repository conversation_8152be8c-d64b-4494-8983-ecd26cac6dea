import { type Request, type Response } from 'express';
import { HttpError } from 'wasp/server';
import crypto from 'crypto';
import { oauthStates, SHOPIFY_APP_CONFIG } from '../actions/initiate-shopify-oauth.action';

type Context = {
  user: {
    id: string;
    email?: string;
    username?: string;
  } | null;
  entities: any;
};

export type ShopifyOAuthCallback = (req: Request, res: Response, context: Context) => Promise<void>;

export const shopifyOAuthCallback: ShopifyOAuthCallback = async (req, res, context) => {
  try {
    const { code, state, shop, hmac } = req.query;

    console.log('[ShopifyOAuth] Callback received:', {
      shop,
      state: state ? 'present' : 'missing',
      code: code ? 'present' : 'missing',
    });

    // Validate required parameters
    if (!code || !state || !shop) {
      throw new HttpError(400, 'Missing required OAuth parameters');
    }

    // Verify HMAC (Shopify security feature)
    if (hmac && !verifyHmac(req.query, hmac as string)) {
      throw new HttpError(400, 'Invalid HMAC signature');
    }

    // Retrieve and validate stored state
    const storedState = oauthStates.get(state as string);
    if (!storedState) {
      throw new HttpError(400, 'Invalid or expired OAuth state');
    }

    // Clean up used state
    oauthStates.delete(state as string);

    // Validate shop matches
    if (storedState.shop !== shop) {
      throw new HttpError(400, 'Shop mismatch in OAuth callback');
    }

    // Exchange code for access token
    const tokenData = await exchangeCodeForToken(storedState.shop, code as string);

    // Store token and shop information
    await storeShopifyIntegration({
      userId: storedState.userId,
      organizationId: storedState.organizationId,
      shop: storedState.shop,
      accessToken: tokenData.access_token,
      scope: tokenData.scope,
      entities: context.entities,
    });

    console.log(`[ShopifyOAuth] Successfully connected shop: ${storedState.shop}`);

    // Redirect to frontend with success - maintain onboarding flow
    const redirectUrl = `${process.env.WASP_WEB_CLIENT_URL || 'http://localhost:3000'}/onboarding?step=product-import&shopify=connected&shop=${storedState.shop}`;
    return res.redirect(redirectUrl);
  } catch (error) {
    console.error('[ShopifyOAuth] Callback failed:', error);

    // Redirect to frontend with error - maintain onboarding flow
    const errorUrl = `${process.env.WASP_WEB_CLIENT_URL || 'http://localhost:3000'}/onboarding?step=product-import&shopify=error&message=${encodeURIComponent(error instanceof Error ? error.message : 'Unknown error')}`;
    return res.redirect(errorUrl);
  }
};

async function exchangeCodeForToken(
  shop: string,
  code: string
): Promise<{
  access_token: string;
  scope: string;
}> {
  const tokenUrl = `https://${shop}/admin/oauth/access_token`;

  const response = await fetch(tokenUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      client_id: SHOPIFY_APP_CONFIG.apiKey,
      client_secret: SHOPIFY_APP_CONFIG.apiSecret,
      code: code,
    }),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
  }

  const tokenData = await response.json();

  if (!tokenData.access_token) {
    throw new Error('No access token received from Shopify');
  }

  return tokenData;
}

function verifyHmac(query: any, hmac: string): boolean {
  const orderedParams = Object.keys(query)
    .filter((key) => key !== 'hmac' && key !== 'signature')
    .sort()
    .map((key) => `${key}=${query[key]}`)
    .join('&');

  const computedHmac = crypto.createHmac('sha256', SHOPIFY_APP_CONFIG.apiSecret).update(orderedParams).digest('hex');

  return computedHmac === hmac;
}

async function storeShopifyIntegration(data: {
  userId: string;
  organizationId: string;
  shop: string;
  accessToken: string;
  scope: string;
  entities: any;
}): Promise<void> {
  try {
    // Check if integration already exists
    const existing = await data.entities.ShopifyIntegration.findUnique({
      where: {
        userId_shop: {
          userId: data.userId,
          shop: data.shop,
        },
      },
    });

    if (existing) {
      // Update existing integration
      await data.entities.ShopifyIntegration.update({
        where: { id: existing.id },
        data: {
          accessToken: data.accessToken, // In production, encrypt this
          scope: data.scope,
          status: 'connected',
          connectedAt: new Date(),
          syncError: null,
        },
      });
    } else {
      // Create new integration
      await data.entities.ShopifyIntegration.create({
        data: {
          userId: data.userId,
          organizationId: data.organizationId,
          shop: data.shop,
          accessToken: data.accessToken, // In production, encrypt this
          scope: data.scope,
          status: 'connected',
          connectedAt: new Date(),
        },
      });
    }

    console.log(`[ShopifyOAuth] Stored integration for shop: ${data.shop}`);
  } catch (error) {
    console.error('[ShopifyOAuth] Failed to store integration:', error);
    throw new Error('Failed to store Shopify integration');
  }
}
