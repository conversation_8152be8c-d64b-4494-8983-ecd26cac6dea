import React, { useState, useEffect, useCallback } from 'react';
import { Group } from 'react-konva';
import KonvaGreenConceptCard from './KonvaGreenConceptCard';
import KonvaLoadingConceptCard from './KonvaLoadingConceptCard';
import NewsletterApprovalCard from './NewsletterApprovalCard';
import KonvaHtmlDisplayCard from './KonvaHtmlDisplayCard';

// Global state to track if the persistent event listener is set up
let globalEventListenerSetup = false;
let globalCardSetter: ((value: React.SetStateAction<any[]>) => void) | null = null;
let globalLoadingCardSetter: ((value: React.SetStateAction<any[]>) => void) | null = null;

// Global event handler that persists across component remounts
const globalHandleCreateConceptCardLocal = (event: CustomEvent<{ cardData: any; cardId: string }>) => {
  console.log('[KonvaConceptCards] 🌍 GLOBAL handleCreateConceptCardLocal called:', event.detail);
  const { cardData, cardId } = event.detail;

  if (globalCardSetter) {
    globalCardSetter((prevCards) => {
      console.log('[KonvaConceptCards] GLOBAL Current cards before adding:', prevCards.length);
      // Check if card already exists to avoid duplicates
      const existingCard = prevCards.find(card => card.id === cardId);
      if (existingCard) {
        console.log('[KonvaConceptCards] GLOBAL Card already exists, skipping:', cardId);
        return prevCards;
      }

      console.log('[KonvaConceptCards] GLOBAL Adding new concept card:', cardId);
      return [...prevCards, cardData];
    });

    // Remove the corresponding loading card if it exists
    if (cardData.loadingCardId && globalLoadingCardSetter) {
      console.log('[KonvaConceptCards] GLOBAL Removing loading card:', cardData.loadingCardId);
      globalLoadingCardSetter((prevCards) => prevCards.filter(card => card.id !== cardData.loadingCardId));
    }
  } else {
    console.warn('[KonvaConceptCards] GLOBAL Card setter not available!');
  }
};

// Set up global event listener once
if (!globalEventListenerSetup) {
  console.log('[KonvaConceptCards] 🌍 Setting up GLOBAL event listener');
  window.addEventListener('createConceptCardLocal', globalHandleCreateConceptCardLocal as EventListener);
  globalEventListenerSetup = true;
}
// 🧠 MIGRATED: Removed old client type imports - now using modular types
import {
  updateConceptCard,
  generateFromConceptCard,
  getModelProduct,
  approveNewsletterOutline,
  submitNewsletterOutlineFeedback,
} from 'wasp/client/operations';
import { useAuthenticatedSocket } from '../../hooks/useAuthenticatedSocket';
// 🧠 MIGRATED: Using modular hooks instead of client hooks
import { useReferenceImages, useVoiceAgentRoom } from '../../hooks';
// 🧠 MIGRATED: Using modular types instead of client types
import {
  NewsletterApprovalCardDataType,
  NewsletterOutline,
  ConceptCardData,
  HtmlDisplayCardDataType,
} from '../../../domain/types';
import { useAuth } from 'wasp/client/auth';
// 🧠 MIGRATED: Using local type definition instead of external websocket types
interface UpdateNewsletterCardStatusData {
  taskId: string;
  requestType: string;
  status: string;
  result?: {
    gmailMockUrl?: string;
    outlineTitle?: string;
    inlinedMobileHtmlUrl?: string;
    inlinedDesktopHtmlUrl?: string;
  };
}

interface KonvaConceptCardsProps {
  onAnswer: (id: string, questionIndex: number, answer: string) => void;
  onGenerate: (id: string) => void;
  onRemove: (id: string) => void;
  getNewCardPosition: () => { x: number; y: number };
  canvasId?: string;
  // Collaboration props
  onLoadingCardCreate?: (cardData: { id: string; position: { x: number; y: number }; requestText: string }) => void;
  onLoadingCardRemove?: (cardId: string) => void;
  onLoadingCardUpdate?: (cardId: string, updates: any) => void;
  onConceptCardCreate?: (cardData: any) => void;
  onConceptCardUpdate?: (cardId: string, updates: any) => void;
  onConceptCardRemove?: (cardId: string) => void;
  // 📰 Newsletter collaboration
  onNewsletterCardCreate?: (cardData: any) => void;
  onNewsletterCardUpdate?: (cardId: string, updates: any) => void;
}

// Interface for loading card data
interface LoadingCardData {
  id: string;
  position: { x: number; y: number };
  requestText: string;
}

// Define the structure of the data expected from the 'request_human_input' WebSocket event
// This interface should match what the server is actually emitting for this event.
interface RequestHumanInputEventData {
  taskId: string;
  threadId: string;
  question: string;
  context?: string;
  options?: any;
}

// Updated event data structure for newsletter_outline_ready
interface NewsletterOutlineReadyEventData {
  taskId: string;
  threadId: string;
  outline: NewsletterOutline; // This is the structured { title, sections } object
  clientLoadingCardId?: string; // Added to identify the loading card to replace
}

// Temporary flag to silence noisy logs – set to true when debugging this component
const DEBUG_KONVA_CARDS = false;
const dbg = (...args: any[]) => {
  if (DEBUG_KONVA_CARDS) console.log('[KonvaConceptCards]', ...args);
};

const KonvaConceptCards: React.FC<KonvaConceptCardsProps> = ({
  onAnswer,
  onGenerate,
  onRemove,
  getNewCardPosition,
  canvasId,
  onLoadingCardCreate,
  onLoadingCardRemove,
  onLoadingCardUpdate,
  onConceptCardCreate,
  onConceptCardUpdate,
  onConceptCardRemove,
  onNewsletterCardCreate,
  onNewsletterCardUpdate,
}) => {
  const [cards, setCards] = useState<ConceptCardData[]>([]);
  const [loadingCards, setLoadingCards] = useState<LoadingCardData[]>([]);

  // Connect global setters to local state
  useEffect(() => {
    console.log('[KonvaConceptCards] 🔗 Connecting global setters to local state');
    globalCardSetter = setCards;
    globalLoadingCardSetter = setLoadingCards;

    return () => {
      console.log('[KonvaConceptCards] 🔗 Disconnecting global setters');
      // Don't set to null here as other instances might be using them
    };
  }, []);
  // const [tasksPendingHtmlGeneration, setTasksPendingHtmlGeneration] = useState<Set<string>>(new Set()); // REMOVE THIS

  // Track loading card positions
  const [loadingCardPositions, setLoadingCardPositions] = useState<Record<string, { x: number; y: number }>>({});
  const [selectedCardId, setSelectedCardId] = useState<string | null>(null);

  // 🔌 MIGRATED: Use modular authenticated socket
  const { socket, isConnected } = useAuthenticatedSocket();
  const whiteboardId = localStorage.getItem('selectedModelId') || '';
  const { data: user } = useAuth();
  const roomName = useVoiceAgentRoom();
  const { getReferenceImages } = useReferenceImages(roomName);
  const [isLoading, setIsLoading] = useState(false);

  // 🧠 MIGRATED: Use local state for newsletter cards (TODO: move to domain state later)
  const [newsletterApprovalCards, setNewsletterApprovalCards] = useState<any[]>([]);
  const [newsletterHtmlDisplayCards, setNewsletterHtmlDisplayCards] = useState<any[]>([]);

  // 🧠 MIGRATED: Stub functions to replace storeActions (newsletter functionality simplified)
  const updateNewsletterApprovalCardStatus = useCallback((params: any) => {
    dbg('Newsletter card status update (stubbed):', params);
    // TODO: Implement proper newsletter card status updates when needed
  }, []);

  const setNewsletterApprovalCardsWithSync = useCallback((params: any) => {
    dbg('Newsletter approval cards sync (stubbed):', params);
    if (params.cards) {
      setNewsletterApprovalCards(params.cards);
    }
  }, []);

  // Memoize handlers passed to effects or children if they don't depend on frequently changing state
  const handleCreateConceptCard = useCallback(
    (cardData: ConceptCardData) => {
      dbg('Received createConceptCard event:', cardData);

      // If this card is replacing a loading card, use the loading card's position if available
      const updatedCardData = { ...cardData };

      if (cardData.loadingCardId && typeof cardData.loadingCardId === 'string') {
        // Check if we have a tracked position for this loading card
        const loadingId = cardData.loadingCardId as string; // Cast to string to satisfy TypeScript
        const loadingCardPosition = loadingCardPositions[loadingId];

        if (loadingCardPosition) {
          dbg(`Using tracked position for loading card ${cardData.loadingCardId}:`, loadingCardPosition);
          updatedCardData.position = loadingCardPosition;
        } else {
          // Try to find the loading card in the loadingCards state
          const loadingCard = loadingCards.find((card) => card.id === cardData.loadingCardId);

          if (loadingCard) {
            dbg(`Using position from loadingCards for ${cardData.loadingCardId}:`, loadingCard.position);
            updatedCardData.position = loadingCard.position;
          }
        }
      }

      // Add the new card with the updated position, ONLY if it doesn't already exist
      setCards((prevCards) => {
        if (prevCards.some((card) => card.id === updatedCardData.id)) {
          dbg(`Card ${updatedCardData.id} already exists, not adding again.`);
          return prevCards; // Return previous cards if duplicate
        }
        return [...prevCards, updatedCardData];
      });

      // Select the new card
      setSelectedCardId(updatedCardData.id);

      // Remove the loading card if this card is replacing one
      if (cardData.loadingCardId && typeof cardData.loadingCardId === 'string') {
        dbg(`Replacing loading card ${cardData.loadingCardId} with real card ${cardData.id}`);
        setLoadingCards((prev) => prev.filter((card) => card.id !== cardData.loadingCardId));

        // Also remove the tracked position
        setLoadingCardPositions((prev) => {
          const newPositions = { ...prev };
          const loadingId = cardData.loadingCardId as string; // Cast to string to satisfy TypeScript
          if (loadingId in newPositions) {
            delete newPositions[loadingId];
          }
          return newPositions;
        });

        // Broadcast loading card removal to collaborators
        if (onLoadingCardRemove) {
          onLoadingCardRemove(cardData.loadingCardId);
        }

        // Also dispatch a local event to remove the loading card from local state
        // This ensures the loading card is removed even if it exists in local state
        window.dispatchEvent(
          new CustomEvent('removeLoadingCard', {
            detail: { id: cardData.loadingCardId },
          })
        );
      }

      // Broadcast concept card creation to collaborators via Cloudflare Workers
      if (onConceptCardCreate) {
        onConceptCardCreate(updatedCardData);
      }

      dbg('Created new concept card:', updatedCardData.id);
    },
    [cards, loadingCards, loadingCardPositions, onConceptCardCreate]
  );

  const handleUpdateConceptCard = useCallback((updateData: Partial<ConceptCardData>) => {
    dbg('Received updateConceptCard event:', updateData);

    if (!updateData.id) return;

    // Log suggestion chips if present
    if (updateData.suggestionChips && updateData.suggestionChips.length > 0) {
      dbg(`Received ${updateData.suggestionChips.length} suggestion chips:`, updateData.suggestionChips);
    }

    setCards((prevCards) => prevCards.map((card) => (card.id === updateData.id ? { ...card, ...updateData } : card)));

    dbg('Updated concept card:', updateData.id);
  }, []);

  const handleUpdateNewsletterCardStatus = useCallback(
    (data: UpdateNewsletterCardStatusData) => {
      console.log('📰 [KonvaConceptCards] Received newsletter status update:', data);

      // 🎯 Handle nested data structure - check if data is in data.data
      const actualData = (data as any).data || data;

      console.log('📰 [KonvaConceptCards] Status update details:', {
        status: actualData.status,
        requestType: actualData.requestType,
        taskId: actualData.taskId,
        hasResult: !!actualData.result,
        result: actualData.result,
        gmailMockUrl: actualData.result?.gmailMockUrl
      });
      dbg('Received updateNewsletterCardStatus:', data);
      if (actualData.requestType === 'generate_newsletter') {
        // 🧠 MIGRATED: Use local state instead of store actions
        const currentApprovalCardsFromStore = newsletterApprovalCards;
        const currentHtmlCardsFromStore = newsletterHtmlDisplayCards;

        if (actualData.status === 'completed' && actualData.result?.gmailMockUrl) {
          console.log('🎉 [KonvaConceptCards] Newsletter COMPLETED! Transitioning to HTML display card');
          console.log('🎉 [KonvaConceptCards] Completion data:', actualData);

          let approvalCardPosition = getNewCardPosition();
          const cardToRemove = currentApprovalCardsFromStore.find((card) => card.taskId === actualData.taskId);

          if (cardToRemove) {
            console.log('🎉 [KonvaConceptCards] Found approval card to remove:', cardToRemove.id);
            approvalCardPosition = cardToRemove.position;
          }
          const updatedApprovalCards = currentApprovalCardsFromStore.filter((card) => card.taskId !== actualData.taskId);

          // 🧠 MIGRATED: Update local state instead of store actions
          console.log('🎉 [KonvaConceptCards] Removing approval card, remaining cards:', updatedApprovalCards.length);
          setNewsletterApprovalCards(updatedApprovalCards);

          const newHtmlCard: HtmlDisplayCardDataType = {
            id: `${actualData.taskId}_html_display`,
            taskId: actualData.taskId,
            title: actualData.result?.outlineTitle || 'Generated Newsletter',
            iframeUrl: actualData.result?.gmailMockUrl || '',
            position: approvalCardPosition,
            inlinedMobileHtmlUrl: actualData.result?.inlinedMobileHtmlUrl,
            inlinedDesktopHtmlUrl: actualData.result?.inlinedDesktopHtmlUrl,
          };

          if (!currentHtmlCardsFromStore.some((card) => card.id === newHtmlCard.id)) {
            // 🧠 MIGRATED: Update local state instead of store actions
            console.log('🎉 [KonvaConceptCards] Creating HTML display card:', newHtmlCard);
            setNewsletterHtmlDisplayCards([...currentHtmlCardsFromStore, newHtmlCard]);
          } else {
            console.log('🎉 [KonvaConceptCards] HTML display card already exists:', newHtmlCard.id);
          }
          dbg(`Added HtmlDisplayCard for ${actualData.taskId}`);
        } else if (actualData.status === 'failed') {
          // 🧠 MIGRATED: Update local state for failed status
          const updatedCards = currentApprovalCardsFromStore.map((card: any) =>
            card.taskId === actualData.taskId ? { ...card, status: 'failed' as const } : card
          );
          setNewsletterApprovalCards(updatedCards);
          dbg(`NewsletterApprovalCard ${actualData.taskId} status updated to failed.`);
        } else {
          // 🧠 MIGRATED: Update local state for intermediate statuses
          const updatedCards = currentApprovalCardsFromStore.map((card: any) =>
            card.taskId === actualData.taskId ? { ...card, status: actualData.status as any } : card
          );
          setNewsletterApprovalCards(updatedCards);
          dbg(`NewsletterApprovalCard ${actualData.taskId} status updated to ${actualData.status}.`);
        }
      }
    },
    [getNewCardPosition, newsletterApprovalCards, newsletterHtmlDisplayCards, socket, whiteboardId]
  );

  const handleNewsletterOutlineReady = useCallback(
    (data: NewsletterOutlineReadyEventData) => {
      dbg('Received newsletter_outline_ready event. Full data:', JSON.stringify(data, null, 2));

      // Robust validation of the incoming data, especially the nested outline structure
      if (!data) {
        dbg('newsletter_outline_ready: Event data is null or undefined.');
        return;
      }
      if (!data.taskId) {
        dbg('newsletter_outline_ready: taskId is missing in event data.');
        return;
      }
      if (!data.threadId) {
        dbg('newsletter_outline_ready: threadId is missing in event data.');
        return;
      }
      if (!data.outline) {
        dbg('newsletter_outline_ready: outline object is missing in event data.');
        return;
      }
      if (typeof data.outline.title !== 'string') {
        dbg('newsletter_outline_ready: outline.title is missing or not a string.');
        return;
      }
      if (!Array.isArray(data.outline.sections)) {
        dbg('newsletter_outline_ready: outline.sections is missing or not an array.');
        return;
      }
      if (
        !data.outline.sections.every(
          (section) =>
            typeof section === 'object' &&
            section !== null &&
            typeof section.heading === 'string' &&
            typeof section.description === 'string'
        )
      ) {
        dbg('newsletter_outline_ready: One or more sections in outline.sections have invalid structure.');
        return;
      }
      dbg('newsletter_outline_ready: Event data passed validation. TaskId:', data.taskId, 'ThreadId:', data.threadId);

      let finalPosition = getNewCardPosition();

      if (data.clientLoadingCardId) {
        dbg(`newsletter_outline_ready: Received clientLoadingCardId: ${data.clientLoadingCardId}`);
        const loadingCardToReplace = loadingCards.find((lc) => lc.id === data.clientLoadingCardId);
        if (loadingCardToReplace) {
          dbg(`Found loading card to replace at position:`, loadingCardToReplace.position);
          finalPosition = loadingCardToReplace.position;
          // Remove the loading card that is being replaced
          setLoadingCards((prevLoadingCards) => prevLoadingCards.filter((lc) => lc.id !== data.clientLoadingCardId));
          // Also remove from loadingCardPositions if it exists there
          setLoadingCardPositions((prevPositions) => {
            const newPositions = { ...prevPositions };
            if (data.clientLoadingCardId && newPositions[data.clientLoadingCardId]) {
              delete newPositions[data.clientLoadingCardId];
            }
            return newPositions;
          });
        } else {
          dbg(`Loading card ${data.clientLoadingCardId} not found, using default position.`);
        }
      } else {
        dbg(`No clientLoadingCardId provided with newsletter_outline_ready event.`);
      }

      const newCardData: NewsletterApprovalCardDataType = {
        id: `newsletter-approval-${data.taskId}`,
        taskId: data.taskId,
        threadId: data.threadId,
        outline: data.outline,
        outlineText: JSON.stringify(data.outline, null, 2),
        position: finalPosition,
        status: 'outline_ready_for_review',
        onApprove: async (params: { taskId: string; outline: NewsletterOutline }) => {
          dbg(`Approve clicked for task:`, params.taskId);
          try {
            // 🧠 MIGRATED: Use stub function instead of store action
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'generating_html',
            });
            await approveNewsletterOutline({ taskId: params.taskId, outline: params.outline });
          } catch (error) {
            dbg(`Error approving newsletter outline for task ${params.taskId}:`, error);
            // 🧠 MIGRATED: Use stub function instead of store action
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'failed',
              error: (error as Error).message,
            });
          }
        },
        onSubmitFeedback: async (params: { taskId: string; feedback: string }) => {
          dbg(`Submit feedback clicked for task:`, params.taskId);
          try {
            // 🧠 MIGRATED: Use stub function instead of store action
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'processing',
            });
            await submitNewsletterOutlineFeedback({ taskId: params.taskId, feedback: params.feedback });
          } catch (error) {
            dbg(`Error submitting newsletter feedback for task ${params.taskId}:`, error);
            // 🧠 MIGRATED: Use stub function instead of store action
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'failed',
              error: (error as Error).message,
            });
          }
        },
      };

      dbg(`Creating or updating NewsletterApprovalCard with data:`, newCardData);
      // 🧠 MIGRATED: Use local state instead of store actions
      const currentApprovalCards = newsletterApprovalCards;
      const existingCardIndex = currentApprovalCards.findIndex((card: any) => card.taskId === data.taskId);
      let finalApprovalCards: any[];

      if (existingCardIndex !== -1) {
        const updatedCards = [...currentApprovalCards];
        updatedCards[existingCardIndex] = {
          ...currentApprovalCards[existingCardIndex],
          ...newCardData,
          position: currentApprovalCards[existingCardIndex].position,
          // Ensure callbacks are preserved from newCardData
          onApprove: newCardData.onApprove,
          onSubmitFeedback: newCardData.onSubmitFeedback,
        };
        finalApprovalCards = updatedCards;
        dbg(`Updated existing NewsletterApprovalCard for taskId: ${data.taskId}`);
      } else {
        finalApprovalCards = [...currentApprovalCards, newCardData];
        dbg(`Added new NewsletterApprovalCard for taskId: ${data.taskId}`);
      }
      // 🧠 MIGRATED: Update local state instead of store
      setNewsletterApprovalCards(finalApprovalCards);

      // 🤝 Broadcast newsletter card creation to collaborators (same as concept cards)
      if (onNewsletterCardCreate && newCardData) {
        console.log('[KonvaConceptCards] 📰 Broadcasting newsletter card creation:', newCardData.id);
        onNewsletterCardCreate(newCardData);
      }
    },
    [
      getNewCardPosition,
      loadingCards,
      loadingCardPositions,
      newsletterApprovalCards,
      socket,
      whiteboardId,
      updateNewsletterApprovalCardStatus,
      onNewsletterCardCreate,
    ]
  );

  // Listen for concept card events from Cloudflare Workers (via useCanvasSync)
  useEffect(() => {
    dbg('Setting up Cloudflare Workers event listeners for concept cards.');

    // Wrapper functions to extract data from CustomEvent.detail
    const handleCreateConceptCardEvent = (event: CustomEvent) => {
      handleCreateConceptCard(event.detail);
    };

    const handleUpdateConceptCardEvent = (event: CustomEvent) => {
      handleUpdateConceptCard(event.detail);
    };

    // Listen for concept card creation events
    window.addEventListener('createConceptCard', handleCreateConceptCardEvent as EventListener);
    window.addEventListener('updateConceptCard', handleUpdateConceptCardEvent as EventListener);
    const handleRequestHumanInput = (eventPayload: any) => {
      const data = eventPayload as RequestHumanInputEventData;
      dbg('Received request_human_input event. Full data:', JSON.stringify(data, null, 2));
      if (!data.taskId || !data.threadId || typeof data.question === 'undefined') {
        dbg('Invalid request_human_input event data received:', data);
        return;
      }
      const defaultPosition = getNewCardPosition(); // Use dynamic position
      const newApprovalCard: NewsletterApprovalCardDataType = {
        id: data.taskId, // Using taskId as card id for simplicity here
        taskId: data.taskId,
        threadId: data.threadId,
        outlineText: data.question,
        agentContext: data.context,
        initialOptions: data.options,
        position: defaultPosition,
        status: 'pending_approval',
        onApprove: async (params: { taskId: string; outline: NewsletterOutline }) => {
          dbg(`Approve clicked for task:`, params.taskId);
          try {
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'generating_html',
              socket,
              whiteboardId,
            });
            await approveNewsletterOutline({ taskId: params.taskId, outline: params.outline });
          } catch (error) {
            dbg(`Error approving newsletter outline for task ${params.taskId}:`, error);
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'failed',
              error: (error as Error).message,
              socket,
              whiteboardId,
            });
          }
        },
        onSubmitFeedback: async (params: { taskId: string; feedback: string }) => {
          dbg(`Submit feedback clicked for task:`, params.taskId);
          try {
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'processing',
              socket,
              whiteboardId,
            });
            await submitNewsletterOutlineFeedback({ taskId: params.taskId, feedback: params.feedback });
          } catch (error) {
            dbg(`Error submitting newsletter feedback for task ${params.taskId}:`, error);
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'failed',
              error: (error as Error).message,
              socket,
              whiteboardId,
            });
          }
        },
      };
      // Add to store and sync
      // 🧠 MIGRATED: Use local state instead of store
      const currentCards = newsletterApprovalCards;
      if (!currentCards.find((card) => card.id === newApprovalCard.id)) {
        setNewsletterApprovalCardsWithSync({
          cards: [...currentCards, newApprovalCard],
          socket,
          whiteboardId,
          cardToSync: newApprovalCard,
        });
      }
    };
    // Wrapper functions for remaining WASP WebSocket events (to be migrated)
    const handleRequestHumanInputEvent = (event: CustomEvent) => {
      handleRequestHumanInput(event.detail);
    };

    const handleNewsletterOutlineReadyEvent = (event: CustomEvent) => {
      handleNewsletterOutlineReady(event.detail);
    };

    const handleUpdateNewsletterCardStatusEvent = (event: CustomEvent) => {
      handleUpdateNewsletterCardStatus(event.detail);
    };

    // Listen for events from Cloudflare Workers
    window.addEventListener('request_human_input', handleRequestHumanInputEvent as EventListener);
    window.addEventListener('newsletter_outline_ready', handleNewsletterOutlineReadyEvent as EventListener);
    window.addEventListener('newsletter_card_status_update', handleUpdateNewsletterCardStatusEvent as EventListener);

    return () => {
      dbg('Cleaning up Cloudflare Workers event listeners');
      window.removeEventListener('createConceptCard', handleCreateConceptCardEvent as EventListener);
      window.removeEventListener('updateConceptCard', handleUpdateConceptCardEvent as EventListener);
      window.removeEventListener('request_human_input', handleRequestHumanInputEvent as EventListener);
      window.removeEventListener('newsletter_outline_ready', handleNewsletterOutlineReadyEvent as EventListener);
      window.removeEventListener('newsletter_card_status_update', handleUpdateNewsletterCardStatusEvent as EventListener);
    };
  }, [
    handleCreateConceptCard,
    handleUpdateConceptCard,
    handleNewsletterOutlineReady,
    handleUpdateNewsletterCardStatus,
    // 🧠 MIGRATED: Removed socket and storeActions dependencies
    whiteboardId,
    getNewCardPosition,
  ]);

  // 🤝 Listen for collaborative newsletter card creation from other users
  useEffect(() => {
    const handleCreateNewsletterCard = (event: CustomEvent) => {
      const { cardData } = event.detail;
      console.log('[KonvaConceptCards] 🤝 Received newsletter card creation from collaboration:', cardData);

      // Add the card to local state if it doesn't already exist
      setNewsletterApprovalCards(currentCards => {
        const exists = currentCards.find(card => card.id === cardData.id);
        if (!exists) {
          console.log('[KonvaConceptCards] 🤝 Adding collaborative newsletter card:', cardData.id);
          return [...currentCards, cardData];
        }
        return currentCards;
      });
    };

    window.addEventListener('createNewsletterCard', handleCreateNewsletterCard as EventListener);

    return () => {
      window.removeEventListener('createNewsletterCard', handleCreateNewsletterCard as EventListener);
    };
  }, []);

  useEffect(() => {
    dbg('Setting up DOM event listeners');
    const handleCreateConceptCardDOMEvent = (event: CustomEvent<ConceptCardData>) =>
      handleCreateConceptCard(event.detail);
    const handleUpdateConceptCardDOMEvent = (event: CustomEvent<Partial<ConceptCardData>>) =>
      handleUpdateConceptCard(event.detail);
    const handleShowLoadingCard = (event: CustomEvent<LoadingCardData>) => {
      const loadingCardData = event.detail;
      setLoadingCards((prev) => [...prev, loadingCardData]);

      // Broadcast loading card creation to collaborators
      if (onLoadingCardCreate) {
        onLoadingCardCreate({
          id: loadingCardData.id,
          position: loadingCardData.position,
          requestText: loadingCardData.requestText || 'Creating concept...',
        });
      }
    };
    const handleRemoveLoadingCard = (event: CustomEvent<{ id: string }>) => {
      const cardId = event.detail.id;
      setLoadingCards((prev) => prev.filter((card) => card.id !== cardId));

      // Broadcast loading card removal to collaborators
      if (onLoadingCardRemove) {
        onLoadingCardRemove(cardId);
      }
    };
    const handleUpdateLoadingCardPosition = (
      event: CustomEvent<{ id: string; position: { x: number; y: number } }>
    ) => {
      const { id, position } = event.detail;

      // Update local loading card position
      setLoadingCardPositions((prev) => ({ ...prev, [id]: position }));
      setLoadingCards((prev) => prev.map((card) => (card.id === id ? { ...card, position } : card)));

      // Broadcast loading card position update to collaborators
      if (onLoadingCardUpdate) {
        onLoadingCardUpdate(id, { position });
      }
    };
    const handleGetLoadingCardPosition = (
      event: CustomEvent<{ id: string; callback: (position: { x: number; y: number } | null) => void }>
    ) => {
      // Prioritize finding the full card data from loadingCards array.
      const loadingCard = loadingCards.find((card) => card.id === event.detail.id);
      let positionToReturn: { x: number; y: number } | null = null;

      if (loadingCard) {
        positionToReturn = loadingCard.position;
      } else {
        // Fallback to checking loadingCardPositions if not found in loadingCards (e.g., if card was created then quickly replaced)
        const posFromCache = loadingCardPositions[event.detail.id];
        if (posFromCache) {
          positionToReturn = posFromCache;
        }
      }
      event.detail.callback(positionToReturn);
    };
    const createStatusChecker = (eventName: string, getCount: () => number) => () =>
      window.dispatchEvent(new CustomEvent(eventName, { detail: { count: getCount() } }));
    const handleCheckLoadingCards = createStatusChecker('loadingCardsStatus', () => loadingCards.length);
    const handleCheckConceptCards = createStatusChecker('conceptCardsStatus', () => cards.length);
    const handleCheckNewsletterApprovalCards = createStatusChecker(
      'newsletterApprovalCardsStatus',
      () => newsletterApprovalCards.length
    );
    const handleCheckHtmlDisplayCards = createStatusChecker(
      'htmlDisplayCardsStatus',
      () => newsletterHtmlDisplayCards.length
    );

    // Handle collaborative position updates for local concept cards
    const handleUpdateConceptCardPosition = (event: CustomEvent<{ cardId: string; position: { x: number; y: number } }>) => {
      const { cardId, position } = event.detail;

      setCards((prevCards) =>
        prevCards.map((card) =>
          card.id === cardId
            ? { ...card, position }
            : card
        )
      );
    };

    // Handle collaborative concept card removal for local concept cards
    const handleRemoveConceptCardCollaborative = (event: CustomEvent<{ cardId: string }>) => {
      const { cardId } = event.detail;

      setCards((prevCards) => prevCards.filter((card) => card.id !== cardId));
      if (selectedCardId === cardId) {
        setSelectedCardId(null);
      }
    };

    // Handle collaborative concept card status updates for local concept cards
    const handleUpdateConceptCardStatus = (event: CustomEvent<{ cardId: string; status: string }>) => {
      const { cardId, status } = event.detail;

      setCards((prevCards) =>
        prevCards.map((card) =>
          card.id === cardId
            ? { ...card, status: status as 'questioning' | 'generating' | 'completed' }
            : card
        )
      );
    };

    // Handle collaborative concept card numImages updates for local concept cards
    const handleUpdateConceptCardNumImages = (event: CustomEvent<{ cardId: string; numImages: number }>) => {
      const { cardId, numImages } = event.detail;

      // Dispatch event to update the specific card's numImages
      window.dispatchEvent(
        new CustomEvent('updateCardNumImages', {
          detail: { cardId, numImages },
        })
      );
    };

    // Handle collaborative concept card inputText updates for local concept cards
    const handleUpdateConceptCardInputText = (event: CustomEvent<{ cardId: string; inputText: string }>) => {
      const { cardId, inputText } = event.detail;

      // Dispatch event to update the specific card's inputText
      window.dispatchEvent(
        new CustomEvent('updateCardInputText', {
          detail: { cardId, inputText },
        })
      );
    };

    console.log('[KonvaConceptCards] Setting up DOM event listeners');
    window.addEventListener('createConceptCard', handleCreateConceptCardDOMEvent as EventListener);
    window.addEventListener('updateConceptCard', handleUpdateConceptCardDOMEvent as EventListener);
    window.addEventListener('updateConceptCardPosition', handleUpdateConceptCardPosition as EventListener);
    window.addEventListener('updateConceptCardStatus', handleUpdateConceptCardStatus as EventListener);
    window.addEventListener('updateConceptCardNumImages', handleUpdateConceptCardNumImages as EventListener);
    window.addEventListener('updateConceptCardInputText', handleUpdateConceptCardInputText as EventListener);
    window.addEventListener('removeConceptCardCollaborative', handleRemoveConceptCardCollaborative as EventListener);
    window.addEventListener('showLoadingConceptCard', handleShowLoadingCard as EventListener);
    window.addEventListener('removeLoadingConceptCard', handleRemoveLoadingCard as EventListener);
    window.addEventListener('updateLoadingCardPosition', handleUpdateLoadingCardPosition as EventListener);
    window.addEventListener('getLoadingCardPosition', handleGetLoadingCardPosition as EventListener);
    window.addEventListener('checkLoadingCards', handleCheckLoadingCards as EventListener);
    window.addEventListener('checkConceptCards', handleCheckConceptCards as EventListener);
    window.addEventListener('checkNewsletterApprovalCards', handleCheckNewsletterApprovalCards as EventListener);
    window.addEventListener('checkHtmlDisplayCards', handleCheckHtmlDisplayCards as EventListener);
    return () => {
      console.log('[KonvaConceptCards] Cleaning up DOM event listeners');
      window.removeEventListener('createConceptCard', handleCreateConceptCardDOMEvent as EventListener);
      window.removeEventListener('updateConceptCard', handleUpdateConceptCardDOMEvent as EventListener);
      window.removeEventListener('updateConceptCardPosition', handleUpdateConceptCardPosition as EventListener);
      window.removeEventListener('updateConceptCardStatus', handleUpdateConceptCardStatus as EventListener);
      window.removeEventListener('updateConceptCardNumImages', handleUpdateConceptCardNumImages as EventListener);
      window.removeEventListener('updateConceptCardInputText', handleUpdateConceptCardInputText as EventListener);
      window.removeEventListener('removeConceptCardCollaborative', handleRemoveConceptCardCollaborative as EventListener);
      window.removeEventListener('showLoadingConceptCard', handleShowLoadingCard as EventListener);
      window.removeEventListener('removeLoadingConceptCard', handleRemoveLoadingCard as EventListener);
      window.removeEventListener('updateLoadingCardPosition', handleUpdateLoadingCardPosition as EventListener);
      window.removeEventListener('getLoadingCardPosition', handleGetLoadingCardPosition as EventListener);
      window.removeEventListener('checkLoadingCards', handleCheckLoadingCards as EventListener);
      window.removeEventListener('checkConceptCards', handleCheckConceptCards as EventListener);
      window.removeEventListener('checkNewsletterApprovalCards', handleCheckNewsletterApprovalCards as EventListener);
      window.removeEventListener('checkHtmlDisplayCards', handleCheckHtmlDisplayCards as EventListener);
    };
  }, [
    loadingCards,
    cards,
    newsletterApprovalCards,
    newsletterHtmlDisplayCards,
    handleCreateConceptCard,
    handleUpdateConceptCard,
    loadingCardPositions,
  ]);

  // Handle card selection
  const handleSelectCard = (id: string) => setSelectedCardId(id);

  // Handle card removal
  const handleRemoveCard = useCallback(
    (id: string) => {
      setCards((prevCards) => prevCards.filter((card) => card.id !== id));
      if (selectedCardId === id) {
        setSelectedCardId(null);
      }

      // Broadcast concept card removal to collaborators
      if (onConceptCardRemove) {
        onConceptCardRemove(id);
      }
    },
    [selectedCardId, onConceptCardRemove]
  );

  // Handle input submission for preview update
  const handleAnswer = async (id: string, questionIndex: number, input: string) => {
    try {
      dbg(`Updating preview for card ${id} with input: ${input}`);

      // Find the card to get its productId
      const card = cards.find((c) => c.id === id);
      const productId = card?.productId;

      if (productId) {
        dbg(`Using product ID for card ${id}: ${productId}`);
      }

      // Get the reference images
      let referenceImages = getReferenceImages();

      // If we have a productId, try to get the reference image
      if (productId && productId !== 'canvas-test') {
        try {
          dbg(`Fetching product reference image for modelId: ${productId}`);
          // Fetch the product to get its reference_file_id
          const product = await getModelProduct({ modelId: productId });

          if (product && product.reference_file_id) {
            dbg(`Found product reference image: ${product.reference_file_id}`);

            // Check if the reference image is already in the array
            if (!referenceImages.includes(product.reference_file_id)) {
              // Add the product reference image to the beginning of the array
              referenceImages = [product.reference_file_id, ...referenceImages];
              dbg(`Added product reference image to reference images`);
            }
          } else {
            dbg(`No product found or no reference_file_id for modelId: ${productId}`);
          }
        } catch (productError) {
          console.warn(
            `[KonvaConceptCards] Error fetching product reference image for modelId ${productId}:`,
            productError
          );
          // Continue without product reference image
        }
      } else if (productId === 'canvas-test') {
        dbg(`Skipping product lookup for test productId: ${productId}`);
      }

      // Call the Wasp action to update the concept card
      await updateConceptCard({
        cardId: id,
        questionIndex: 0, // Always use index 0 for the single input
        answer: input,
        userId: user?.id || '', // Use actual user ID
        productId, // Include the product ID if available
        referenceImages, // Include reference images for preview generation
      });

      // Also call the provided callback
      onAnswer(id, questionIndex, input);
    } catch (error) {
      dbg(`Error updating concept card:`, error);
    }
  };

  // Handle generate button click
  const handleGenerate = async (cardId: string, numImages?: number) => {
    dbg(`Generate clicked for card:`, cardId, `with ${numImages || 1} images`);
    setIsLoading(true);

    // Find the card data to potentially get productId/modelId
    const cardData = cards.find((card) => card.id === cardId);
    if (!cardData) {
      dbg(`Could not find card data for generation`);
      setIsLoading(false);
      return;
    }

    // *** ASSUMPTION: cardData contains productId ***
    // If not, this needs to be fetched or stored differently.
    const productId = cardData.productId;
    dbg(`Using productId: ${productId} for generation task`);

    try {
      // Get the full list of reference images from the bar
      let referenceImages = getReferenceImages();

      // If we have a productId, try to get the reference image
      if (productId && productId !== 'canvas-test') {
        try {
          dbg(`Fetching product reference image for modelId: ${productId}`);
          // Fetch the product to get its reference_file_id
          const product = await getModelProduct({ modelId: productId });

          if (product && product.reference_file_id) {
            dbg(`Found product reference image: ${product.reference_file_id}`);

            // Check if the reference image is already in the array
            if (!referenceImages.includes(product.reference_file_id)) {
              // Add the product reference image to the beginning of the array
              referenceImages = [product.reference_file_id, ...referenceImages];
              dbg(`Added product reference image to reference images`);
            }
          } else {
            dbg(`No product found or no reference_file_id for modelId: ${productId}`);
          }
        } catch (productError) {
          console.warn(
            `[KonvaConceptCards] Error fetching product reference image for modelId ${productId}:`,
            productError
          );
          // Continue without product reference image
        }
      } else if (productId === 'canvas-test') {
        dbg(`Skipping product lookup for test productId: ${productId}`);
      }

      dbg(`Passing ${referenceImages.length} reference images for generation task`);



      await generateFromConceptCard({
        cardId,
        userId: user?.id || 1,
        productId: productId,
        referenceImages: referenceImages,
        aspectRatio: '1024x1024', // Default to square for now, can be made configurable later
        numImages: numImages || 1, // Pass the number of images to generate
        canvasId: canvasId, // Pass the canvas ID for proper room targeting
      } as any);
      // Card status update will come via WebSocket
    } catch (error) {
      dbg(`Error generating from concept card:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  // Log when cards are added or removed
  useEffect(() => {
    dbg(`Current cards:`, cards);
    window.dispatchEvent(new CustomEvent('conceptCardsStatus', { detail: { count: cards.length } }));
  }, [cards]);

  useEffect(() => {
    dbg(`Current loading cards:`, loadingCards);
    window.dispatchEvent(new CustomEvent('loadingCardsStatus', { detail: { count: loadingCards.length } }));
  }, [loadingCards]);

  useEffect(() => {
    // Removed spammy newsletter approval cards log
    // window.dispatchEvent(new CustomEvent('newsletterApprovalCardsStatus', { detail: { count: newsletterApprovalCards.length } })); // Commented out for debugging
  }, [newsletterApprovalCards]);

  useEffect(() => {
    // Removed spammy HTML display cards log
    // window.dispatchEvent(new CustomEvent('htmlDisplayCardsStatus', { detail: { count: newsletterHtmlDisplayCards.length } })); // Commented out for debugging
  }, [newsletterHtmlDisplayCards]);

  // Removed spammy newsletter approval cards log

  // Create a function to add callbacks to newsletter approval cards that don't have them
  const addCallbacksToCard = useCallback(
    (card: NewsletterApprovalCardDataType): NewsletterApprovalCardDataType => {
      // If the card already has callbacks, return as is
      if (card.onApprove && card.onSubmitFeedback) {
        return card;
      }

      // Add missing callbacks
      return {
        ...card,
        onApprove: async (params: { taskId: string; outline: NewsletterOutline }) => {
          dbg(`Approve clicked for task:`, params.taskId);
          try {
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'generating_html',
              socket,
              whiteboardId,
            });
            await approveNewsletterOutline({ taskId: params.taskId, outline: params.outline });
          } catch (error) {
            dbg(`Error approving newsletter outline for task ${params.taskId}:`, error);
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'failed',
              error: (error as Error).message,
              socket,
              whiteboardId,
            });
          }
        },
        onSubmitFeedback: async (params: { taskId: string; feedback: string }) => {
          dbg(`Submit feedback clicked for task:`, params.taskId);
          try {
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'processing',
              socket,
              whiteboardId,
            });
            await submitNewsletterOutlineFeedback({ taskId: params.taskId, feedback: params.feedback });
          } catch (error) {
            dbg(`Error submitting newsletter feedback for task ${params.taskId}:`, error);
            updateNewsletterApprovalCardStatus({
              taskId: params.taskId,
              status: 'failed',
              error: (error as Error).message,
              socket,
              whiteboardId,
            });
          }
        },
      };
    },
    [socket, whiteboardId, updateNewsletterApprovalCardStatus]
  );

  // Effect to ensure all newsletter approval cards have callbacks
  useEffect(() => {
    const cardsNeedingCallbacks = newsletterApprovalCards.filter((card) => !card.onApprove || !card.onSubmitFeedback);

    if (cardsNeedingCallbacks.length > 0) {
      dbg(`Adding callbacks to ${cardsNeedingCallbacks.length} newsletter approval cards`);
      const updatedCards = newsletterApprovalCards.map(addCallbacksToCard);
      setNewsletterApprovalCardsWithSync({ cards: updatedCards });
    }
  }, [newsletterApprovalCards, addCallbacksToCard, setNewsletterApprovalCardsWithSync]);

  return (
    <Group>
      {/* Render loading cards */}
      {loadingCards.map((card) => (
        <KonvaLoadingConceptCard key={card.id} id={card.id} position={card.position} requestText={card.requestText} />
      ))}

      {/* Render real cards */}
      {cards.map((card) => (
        <KonvaGreenConceptCard
          key={card.id}
          card={card}
          onAnswer={handleAnswer}
          onGenerate={(cardId, numImages) => handleGenerate(cardId, numImages)}
          onRemove={handleRemoveCard}
          isSelected={selectedCardId === card.id}
          onSelect={() => handleSelectCard(card.id)}
          onPositionUpdate={(cardId, position) => {
            // Broadcast position update to collaborators
            if (onConceptCardUpdate) {
              onConceptCardUpdate(cardId, { position });
            }
          }}
          onStatusUpdate={(cardId, status) => {
            // Broadcast status update to collaborators
            if (onConceptCardUpdate) {
              onConceptCardUpdate(cardId, { status });
            }
          }}
        />
      ))}

      {/* Render Newsletter Approval Cards */}
      {newsletterApprovalCards.map((cardData) => {
        return (
          <NewsletterApprovalCard
            key={cardData.id}
            cardData={cardData} // This now comes from Zustand store
            onApprove={cardData.onApprove} // These callbacks are defined when cardData is created
            onSubmitFeedback={cardData.onSubmitFeedback}
            onCardUpdate={onNewsletterCardUpdate} // 🤝 Pass collaboration function
          />
        );
      })}

      {/* Render HTML Display Cards - Temporarily Commented Out */}
      {newsletterHtmlDisplayCards.map((cardData) => (
        <KonvaHtmlDisplayCard
          key={cardData.id}
          cardData={cardData} // This now comes from Zustand store
          onCardUpdate={onNewsletterCardUpdate} // 🤝 Add newsletter collaboration support
        />
      ))}
    </Group>
  );
};

export default KonvaConceptCards;
