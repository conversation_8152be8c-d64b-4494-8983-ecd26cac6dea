/**
 * 🗄️ Brand Kit Repository Implementation
 *
 * @description Prisma-based implementation of brand kit repository
 * @responsibility Handles database operations for brand kits using Prisma
 * @dependencies Prisma entities, BrandKit domain entity, BaseRepository
 * @ai_context This implements the repository interface using Prisma for database operations
 *
 * @example
 * ```typescript
 * const repository = new BrandKitRepositoryImpl(entities);
 *
 * const brandKit = await repository.findById('brand_123');
 * await repository.save(brandKit);
 * ```
 */

// Using type from repository interface
// import type { PaginatedResult } from '../../../core';
import { BrandKit, BrandKitStatus } from '../../domain/entities/brand-kit.entity';
import type {
  IBrandKitRepository,
  BrandKitListOptions,
  BrandKitSearchOptions,
  BrandKitStatistics,
  PaginatedResult,
} from '../../domain/repositories/brand-kit.repository';
import type { BrandKit as BrandKitEntity } from 'wasp/entities';

/**
 * 🗄️ Brand Kit Repository Implementation
 * @ai_context Prisma-based implementation of the brand kit repository interface
 */
export class BrandKitRepositoryImpl implements IBrandKitRepository {
  constructor(private readonly entities: any) {}

  /**
   * 💾 Save a brand kit (create or update)
   * @param brandKit Brand kit to save
   */
  async save(brandKit: BrandKit): Promise<void> {
    const entityData = brandKit.toEntity();

    await this.entities.BrandKit.upsert({
      where: { id: brandKit.id },
      create: entityData,
      update: entityData,
    });
  }

  /**
   * 🔍 Find brand kit by ID
   * @param id Brand kit ID
   * @returns Brand kit or null if not found
   */
  async findById(id: string): Promise<BrandKit | null> {
    const entity = await this.entities.BrandKit.findUnique({
      where: {
        id,
        deletedAt: null, // Only return non-deleted brand kits
      },
    });

    return entity ? BrandKit.fromEntity(entity) : null;
  }

  /**
   * 🔍 Find brand kit by name and organization
   * @param name Brand kit name
   * @param organizationId Organization ID
   * @returns Brand kit or null if not found
   */
  async findByNameAndOrganization(name: string, organizationId: string): Promise<BrandKit | null> {
    const entity = await this.entities.BrandKit.findFirst({
      where: {
        name,
        organizationId,
        deletedAt: null,
      },
    });

    return entity ? BrandKit.fromEntity(entity) : null;
  }

  /**
   * 📋 Find brand kits by organization
   * @param organizationId Organization ID
   * @returns Array of brand kits
   */
  async findByOrganization(organizationId: string): Promise<BrandKit[]> {
    const entities = await this.entities.BrandKit.findMany({
      where: {
        organizationId,
        deletedAt: null,
      },
      orderBy: { updatedAt: 'desc' },
    });

    return entities.map((entity: BrandKitEntity) => BrandKit.fromEntity(entity));
  }

  /**
   * 📋 Find brand kits by user
   * @param userId User ID
   * @returns Array of brand kits
   */
  async findByUser(userId: string): Promise<BrandKit[]> {
    const entities = await this.entities.BrandKit.findMany({
      where: {
        userId,
        deletedAt: null,
      },
      orderBy: { updatedAt: 'desc' },
    });

    return entities.map((entity: BrandKitEntity) => BrandKit.fromEntity(entity));
  }

  /**
   * 📋 Find brand kits with filtering and pagination
   * @param options List options
   * @returns Paginated brand kits
   */
  async findMany(options: BrandKitListOptions = {}): Promise<PaginatedResult<BrandKit>> {
    const {
      organizationId,
      userId,
      status,
      tags,
      search,
      page = 1,
      limit = 20,
      sortBy = 'updatedAt',
      sortOrder = 'desc',
    } = options;

    // Build where clause
    const where: any = {
      deletedAt: null,
    };

    if (organizationId) {
      where.organizationId = organizationId;
    }

    if (userId) {
      where.userId = userId;
    }

    if (status) {
      // Map status to deletedAt field
      if (status === BrandKitStatus.ARCHIVED) {
        where.deletedAt = { not: null };
      } else {
        where.deletedAt = null;
      }
    }

    if (tags && tags.length > 0) {
      where.tags = {
        hasSome: tags,
      };
    }

    if (search) {
      where.OR = [{ name: { contains: search, mode: 'insensitive' } }, { tags: { hasSome: [search] } }];
    }

    // Build order by
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute queries
    const [entities, total] = await Promise.all([
      this.entities.BrandKit.findMany({
        where,
        orderBy,
        skip,
        take: limit,
      }),
      this.entities.BrandKit.count({ where }),
    ]);

    const brandKits = entities.map((entity: BrandKitEntity) => BrandKit.fromEntity(entity));
    const totalPages = Math.ceil(total / limit);

    return {
      data: brandKits,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  /**
   * 🔍 Search brand kits
   * @param query Search query
   * @param organizationId Organization ID
   * @param options Additional search options
   * @returns Matching brand kits
   */
  async search(
    query: string,
    organizationId: string,
    options: Partial<BrandKitSearchOptions> = {}
  ): Promise<BrandKit[]> {
    const { userId, includeArchived = false, limit = 50 } = options;

    const where: any = {
      organizationId,
      OR: [
        { name: { contains: query, mode: 'insensitive' } },
        { tags: { hasSome: [query] } },
        { brandPersonalityText: { contains: query, mode: 'insensitive' } },
        { brandValuesText: { contains: query, mode: 'insensitive' } },
      ],
    };

    if (!includeArchived) {
      where.deletedAt = null;
    }

    if (userId) {
      where.userId = userId;
    }

    const entities = await this.entities.BrandKit.findMany({
      where,
      orderBy: { updatedAt: 'desc' },
      take: limit,
    });

    return entities.map((entity: BrandKitEntity) => BrandKit.fromEntity(entity));
  }

  /**
   * 🗑️ Delete a brand kit (soft delete)
   * @param id Brand kit ID
   */
  async delete(id: string): Promise<void> {
    await this.entities.BrandKit.update({
      where: { id },
      data: {
        deletedAt: new Date(),
      },
    });
  }

  /**
   * 🗑️ Permanently delete a brand kit
   * @param id Brand kit ID
   */
  async permanentlyDelete(id: string): Promise<void> {
    await this.entities.BrandKit.delete({
      where: { id },
    });
  }

  /**
   * 🔄 Restore a soft-deleted brand kit
   * @param id Brand kit ID
   */
  async restore(id: string): Promise<void> {
    await this.entities.BrandKit.update({
      where: { id },
      data: {
        deletedAt: null,
      },
    });
  }

  /**
   * ✅ Check if brand kit exists
   * @param id Brand kit ID
   * @returns True if exists, false otherwise
   */
  async exists(id: string): Promise<boolean> {
    const count = await this.entities.BrandKit.count({
      where: {
        id,
        deletedAt: null,
      },
    });
    return count > 0;
  }

  /**
   * 🔢 Count brand kits
   * @param options Filter options
   * @returns Count of matching brand kits
   */
  async count(options: Partial<BrandKitListOptions> = {}): Promise<number> {
    const where: any = { deletedAt: null };

    if (options.organizationId) {
      where.organizationId = options.organizationId;
    }

    if (options.userId) {
      where.userId = options.userId;
    }

    if (options.status) {
      // Map status to deletedAt field
      if (options.status === BrandKitStatus.ARCHIVED) {
        where.deletedAt = { not: null };
      } else {
        where.deletedAt = null;
      }
    }

    if (options.tags && options.tags.length > 0) {
      where.tags = { hasSome: options.tags };
    }

    return await this.entities.BrandKit.count({ where });
  }

  /**
   * 📊 Get brand kit statistics
   * @param organizationId Organization ID
   * @returns Statistical data
   */
  async getStatistics(organizationId: string): Promise<BrandKitStatistics> {
    const brandKits = await this.findByOrganization(organizationId);

    // Calculate status counts (based on deletedAt field)
    const statusCounts = Object.values(BrandKitStatus).reduce(
      (acc, status) => {
        if (status === BrandKitStatus.ARCHIVED) {
          acc[status] = brandKits.filter((bk) => bk.deletedAt !== null).length;
        } else {
          acc[status] = brandKits.filter((bk) => bk.deletedAt === null).length;
        }
        return acc;
      },
      {} as Record<BrandKitStatus, number>
    );

    // Calculate average completion
    const completionData = brandKits.map((bk) => bk.getCompletionStatus());
    const averageCompletionPercentage =
      completionData.length > 0
        ? Math.round(completionData.reduce((sum, c) => sum + c.completionPercentage, 0) / completionData.length)
        : 0;

    // Get recently updated
    const recentlyUpdated = brandKits.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()).slice(0, 5);

    // Calculate most used tags
    const tagCounts = new Map<string, number>();
    brandKits.forEach((bk) => {
      bk.tags.forEach((tag) => {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
      });
    });

    const mostUsedTags = Array.from(tagCounts.entries())
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalCount: brandKits.length,
      statusCounts,
      averageCompletionPercentage,
      recentlyUpdated,
      mostUsedTags,
    };
  }

  /**
   * 🏷️ Get all unique tags
   * @param organizationId Organization ID
   * @returns Array of unique tags
   */
  async getAllTags(organizationId: string): Promise<string[]> {
    const brandKits = await this.findByOrganization(organizationId);
    const allTags = brandKits.flatMap((bk) => bk.tags);
    return Array.from(new Set(allTags)).sort();
  }

  /**
   * 🏷️ Get popular tags
   * @param organizationId Organization ID
   * @param limit Maximum number of tags to return
   * @returns Array of popular tags with counts
   */
  async getPopularTags(organizationId: string, limit = 10): Promise<Array<{ tag: string; count: number }>> {
    const statistics = await this.getStatistics(organizationId);
    return statistics.mostUsedTags.slice(0, limit);
  }

  /**
   * 📅 Find brand kits updated since date
   * @param since Date to check from
   * @param organizationId Organization ID
   * @returns Brand kits updated since the date
   */
  async findUpdatedSince(since: Date, organizationId: string): Promise<BrandKit[]> {
    const entities = await this.entities.BrandKit.findMany({
      where: {
        organizationId,
        updatedAt: { gte: since },
        deletedAt: null,
      },
      orderBy: { updatedAt: 'desc' },
    });

    return entities.map((entity: BrandKitEntity) => BrandKit.fromEntity(entity));
  }

  /**
   * 📅 Find brand kits created in date range
   * @param startDate Start date
   * @param endDate End date
   * @param organizationId Organization ID
   * @returns Brand kits created in the range
   */
  async findCreatedInRange(startDate: Date, endDate: Date, organizationId: string): Promise<BrandKit[]> {
    const entities = await this.entities.BrandKit.findMany({
      where: {
        organizationId,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        deletedAt: null,
      },
      orderBy: { createdAt: 'desc' },
    });

    return entities.map((entity: BrandKitEntity) => BrandKit.fromEntity(entity));
  }

  /**
   * 🔄 Update brand kit status
   * @param id Brand kit ID
   * @param status New status
   */
  async updateStatus(id: string, status: BrandKitStatus): Promise<void> {
    await this.entities.BrandKit.update({
      where: { id },
      data: {
        status,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 🏷️ Add tags to brand kit
   * @param id Brand kit ID
   * @param tags Tags to add
   */
  async addTags(id: string, tags: string[]): Promise<void> {
    const brandKit = await this.entities.BrandKit.findUnique({
      where: { id },
      select: { tags: true },
    });

    if (brandKit) {
      const newTags = Array.from(new Set([...brandKit.tags, ...tags]));
      await this.entities.BrandKit.update({
        where: { id },
        data: {
          tags: newTags,
          updatedAt: new Date(),
        },
      });
    }
  }

  /**
   * 🏷️ Remove tags from brand kit
   * @param id Brand kit ID
   * @param tags Tags to remove
   */
  async removeTags(id: string, tags: string[]): Promise<void> {
    const brandKit = await this.entities.BrandKit.findUnique({
      where: { id },
      select: { tags: true },
    });

    if (brandKit) {
      const newTags = brandKit.tags.filter((tag: string) => !tags.includes(tag));
      await this.entities.BrandKit.update({
        where: { id },
        data: {
          tags: newTags,
          updatedAt: new Date(),
        },
      });
    }
  }

  /**
   * 📋 Find brand kits by tags
   * @param tags Tags to search for
   * @param organizationId Organization ID
   * @param matchAll Whether to match all tags or any tag
   * @returns Brand kits with matching tags
   */
  async findByTags(tags: string[], organizationId: string, matchAll = false): Promise<BrandKit[]> {
    const where: any = {
      organizationId,
      deletedAt: null,
    };

    if (matchAll) {
      // All tags must be present
      where.tags = { hasEvery: tags };
    } else {
      // Any tag can be present
      where.tags = { hasSome: tags };
    }

    const entities = await this.entities.BrandKit.findMany({
      where,
      orderBy: { updatedAt: 'desc' },
    });

    return entities.map((entity: BrandKitEntity) => BrandKit.fromEntity(entity));
  }

  /**
   * 🔄 Bulk update brand kits
   * @param ids Brand kit IDs
   * @param updates Updates to apply
   */
  async bulkUpdate(ids: string[], updates: Partial<BrandKit>): Promise<void> {
    await this.entities.BrandKit.updateMany({
      where: { id: { in: ids } },
      data: {
        ...updates,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * 📊 Get completion statistics
   * @param organizationId Organization ID
   * @returns Completion statistics
   */
  async getCompletionStatistics(organizationId: string): Promise<{
    totalBrandKits: number;
    completeBrandKits: number;
    averageCompletion: number;
    completionBySection: Record<string, number>;
  }> {
    const brandKits = await this.findByOrganization(organizationId);
    const completionData = brandKits.map((bk) => bk.getCompletionStatus());

    const completeBrandKits = completionData.filter((c) => c.isComplete).length;
    const averageCompletion =
      completionData.length > 0
        ? Math.round(completionData.reduce((sum, c) => sum + c.completionPercentage, 0) / completionData.length)
        : 0;

    // Calculate completion by section
    const sectionCounts = {
      colors: 0,
      typography: 0,
      logos: 0,
      photography: 0,
      brand_voice: 0,
      ai_settings: 0,
    };

    brandKits.forEach((bk) => {
      if (bk.colorPalette.primary.length > 0) sectionCounts.colors++;
      if (bk.typography.heading.fontFamily !== '') sectionCounts.typography++;
      if (bk.logoVariations.length > 0) sectionCounts.logos++;
      if (bk.photographyGuidelines.style !== '') sectionCounts.photography++;
      if (bk.brandVoice.personality.length > 0) sectionCounts.brand_voice++;
      if (bk.aiGenerationSettings.promptKeywords.length > 0) sectionCounts.ai_settings++;
    });

    const completionBySection = Object.entries(sectionCounts).reduce(
      (acc, [section, count]) => {
        acc[section] = brandKits.length > 0 ? Math.round((count / brandKits.length) * 100) : 0;
        return acc;
      },
      {} as Record<string, number>
    );

    return {
      totalBrandKits: brandKits.length,
      completeBrandKits,
      averageCompletion,
      completionBySection,
    };
  }
}
