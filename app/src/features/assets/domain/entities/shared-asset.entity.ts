import { z } from 'zod';

// Sharing permission levels
export enum SharingPermission {
  VIEW = 'view',
  COMMENT = 'comment',
  DOWNLOAD = 'download',
  EDIT = 'edit',
}

// Sharing settings interface
export interface SharingSettings {
  allowDownload: boolean;
  allowComments: boolean;
  requirePassword: boolean;
  password?: string;
  watermark: boolean;
  trackViews: boolean;
}

// Shared asset interface
export interface SharedAsset {
  id: string;
  assetId: number;
  sharedByUserId: string;
  sharedWithEmail: string | null; // null for public links
  token: string;
  permissions: SharingPermission[];
  settings: SharingSettings;
  expiresAt: Date | null;
  isActive: boolean;
  viewCount: number;
  downloadCount: number;
  lastAccessedAt: Date | null;
  createdAt: Date;
  updatedAt: Date;

  // Related entities (optional for domain entity)
  asset?: any;
  sharedBy?: any;
}

// Validation schemas
export const SharingSettingsSchema = z.object({
  allowDownload: z.boolean().default(true),
  allowComments: z.boolean().default(false),
  requirePassword: z.boolean().default(false),
  password: z.string().optional(),
  watermark: z.boolean().default(false),
  trackViews: z.boolean().default(true),
});

export const CreateSharedAssetSchema = z.object({
  assetId: z.number(),
  sharedWithEmail: z.string().email().optional(),
  permissions: z.array(z.nativeEnum(SharingPermission)).default([SharingPermission.VIEW]),
  settings: SharingSettingsSchema.default({}),
  expiresAt: z.date().optional(),
});

export const UpdateSharedAssetSchema = z.object({
  permissions: z.array(z.nativeEnum(SharingPermission)).optional(),
  settings: SharingSettingsSchema.optional(),
  expiresAt: z.date().optional(),
  isActive: z.boolean().optional(),
});

// Shared asset domain class with business logic
export class SharedAssetEntity {
  constructor(private sharedAsset: SharedAsset) {}

  // Getters
  get id(): string {
    return this.sharedAsset.id;
  }

  get assetId(): number {
    return this.sharedAsset.assetId;
  }

  get sharedByUserId(): string {
    return this.sharedAsset.sharedByUserId;
  }

  get sharedWithEmail(): string | null {
    return this.sharedAsset.sharedWithEmail;
  }

  get token(): string {
    return this.sharedAsset.token;
  }

  get permissions(): SharingPermission[] {
    return this.sharedAsset.permissions;
  }

  get settings(): SharingSettings {
    return this.sharedAsset.settings;
  }

  get expiresAt(): Date | null {
    return this.sharedAsset.expiresAt;
  }

  get isActive(): boolean {
    return this.sharedAsset.isActive;
  }

  get viewCount(): number {
    return this.sharedAsset.viewCount;
  }

  get downloadCount(): number {
    return this.sharedAsset.downloadCount;
  }

  get lastAccessedAt(): Date | null {
    return this.sharedAsset.lastAccessedAt;
  }

  get createdAt(): Date {
    return this.sharedAsset.createdAt;
  }

  get updatedAt(): Date {
    return this.sharedAsset.updatedAt;
  }

  // Business logic methods
  isExpired(): boolean {
    if (!this.sharedAsset.expiresAt) return false;
    return new Date() > this.sharedAsset.expiresAt;
  }

  isValid(): boolean {
    return this.sharedAsset.isActive && !this.isExpired();
  }

  isPublicLink(): boolean {
    return this.sharedAsset.sharedWithEmail === null;
  }

  isPrivateShare(): boolean {
    return this.sharedAsset.sharedWithEmail !== null;
  }

  canView(): boolean {
    return this.isValid() && this.hasPermission(SharingPermission.VIEW);
  }

  canComment(): boolean {
    return this.isValid() && this.hasPermission(SharingPermission.COMMENT);
  }

  canDownload(): boolean {
    return this.isValid() && this.hasPermission(SharingPermission.DOWNLOAD) && this.sharedAsset.settings.allowDownload;
  }

  canEdit(): boolean {
    return this.isValid() && this.hasPermission(SharingPermission.EDIT);
  }

  hasPermission(permission: SharingPermission): boolean {
    return this.sharedAsset.permissions.includes(permission);
  }

  requiresPassword(): boolean {
    return this.sharedAsset.settings.requirePassword;
  }

  shouldShowWatermark(): boolean {
    return this.sharedAsset.settings.watermark;
  }

  shouldTrackViews(): boolean {
    return this.sharedAsset.settings.trackViews;
  }

  // Update methods
  recordView(): void {
    if (this.shouldTrackViews()) {
      this.sharedAsset.viewCount += 1;
      this.sharedAsset.lastAccessedAt = new Date();
      this.sharedAsset.updatedAt = new Date();
    }
  }

  recordDownload(): void {
    if (this.canDownload()) {
      this.sharedAsset.downloadCount += 1;
      this.sharedAsset.lastAccessedAt = new Date();
      this.sharedAsset.updatedAt = new Date();
    }
  }

  updatePermissions(permissions: SharingPermission[]): void {
    this.sharedAsset.permissions = permissions;
    this.sharedAsset.updatedAt = new Date();
  }

  updateSettings(settings: Partial<SharingSettings>): void {
    this.sharedAsset.settings = { ...this.sharedAsset.settings, ...settings };
    this.sharedAsset.updatedAt = new Date();
  }

  updateExpiration(expiresAt: Date | null): void {
    this.sharedAsset.expiresAt = expiresAt;
    this.sharedAsset.updatedAt = new Date();
  }

  deactivate(): void {
    this.sharedAsset.isActive = false;
    this.sharedAsset.updatedAt = new Date();
  }

  activate(): void {
    this.sharedAsset.isActive = true;
    this.sharedAsset.updatedAt = new Date();
  }

  // Static factory methods
  static fromPrismaSharedAsset(prismaSharedAsset: any): SharedAssetEntity {
    return new SharedAssetEntity(prismaSharedAsset as SharedAsset);
  }

  static createNew(
    data: z.infer<typeof CreateSharedAssetSchema> & {
      sharedByUserId: string;
      token: string;
    }
  ): Partial<SharedAsset> {
    return {
      assetId: data.assetId,
      sharedByUserId: data.sharedByUserId,
      sharedWithEmail: data.sharedWithEmail || null,
      token: data.token,
      permissions: data.permissions,
      settings: data.settings,
      expiresAt: data.expiresAt || null,
      isActive: true,
      viewCount: 0,
      downloadCount: 0,
      lastAccessedAt: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  // Utility methods
  static generateToken(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  static getDefaultSettings(): SharingSettings {
    return {
      allowDownload: true,
      allowComments: false,
      requirePassword: false,
      watermark: false,
      trackViews: true,
    };
  }

  static getDefaultPermissions(): SharingPermission[] {
    return [SharingPermission.VIEW];
  }

  // Convert to plain object
  toPlainObject(): SharedAsset {
    return this.sharedAsset;
  }

  // Get summary information
  getSummary(): {
    id: string;
    assetId: number;
    isPublicLink: boolean;
    sharedWithEmail: string | null;
    permissions: SharingPermission[];
    isExpired: boolean;
    isActive: boolean;
    viewCount: number;
    downloadCount: number;
    createdAt: Date;
    expiresAt: Date | null;
  } {
    return {
      id: this.id,
      assetId: this.assetId,
      isPublicLink: this.isPublicLink(),
      sharedWithEmail: this.sharedWithEmail,
      permissions: this.permissions,
      isExpired: this.isExpired(),
      isActive: this.isActive,
      viewCount: this.viewCount,
      downloadCount: this.downloadCount,
      createdAt: this.createdAt,
      expiresAt: this.expiresAt,
    };
  }
}
