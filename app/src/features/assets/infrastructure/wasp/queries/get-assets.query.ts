import { Asset } from 'wasp/entities';
import { type GetAssets } from 'wasp/server/operations';
import { authenticateUser } from '../../../../../server/helpers';
import { HttpError } from 'wasp/server';
import { Prisma } from '@prisma/client';

// Inline URL cleaner function
const cleanUrl = (url: string): string => {
  // Remove any duplicate protocol prefixes
  const cleanedUrl = url.replace(/^(https?:\/\/)+(https?:\/\/)/, '$1');

  // Ensure the URL starts with https://
  if (!cleanedUrl.startsWith('https://')) {
    return 'https://' + cleanedUrl.replace(/^http:\/\//, '');
  }

  return cleanedUrl;
};

export const getAssets: GetAssets<
  {
    search?: string;
    tags?: string[];
    collections?: string[];
    sources?: string[]; // Add source filtering
    sort?: 'newest' | 'oldest' | 'name';
    page?: number;
    limit?: number;
    organizationId: string;
    filter?: 'all' | 'recent' | 'owned' | 'deleted';
  },
  { assets: Asset[]; total: number }
> = async (args, context) => {
  const currentUser = authenticateUser(context);
  const { search, tags, collections, sources, sort = 'newest', page = 1, limit = 20, filter = 'all' } = args || {};

  // Build where clause
  const where: Prisma.AssetWhereInput = {
    organizationId: args.organizationId,
    deletedAt: null,
    isCover: true,
    parentAssetId: null,
  };

  // Apply filters based on type
  switch (filter) {
    case 'recent':
      where.uploadedAt = {
        gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
      };
      where.deletedAt = null;
      break;
    case 'owned':
      where.userId = currentUser.id;
      where.deletedAt = null;
      break;
    case 'deleted':
      where.deletedAt = { not: null };
      break;
    case 'all':
    default:
      where.deletedAt = null;
      break;
  }

  // Add search condition
  if (search) {
    where.OR = [
      { fileName: { contains: search, mode: 'insensitive' } },
      { tags: { some: { name: { contains: search, mode: 'insensitive' } } } },
    ];
  }

  // Add tags filter
  if (tags?.length) {
    where.tags = {
      some: {
        name: { in: tags },
      },
    };
  }

  // Add collections filter
  if (collections?.length) {
    where.collections = {
      some: {
        id: { in: collections },
      },
    };
  }

  // Add sources filter
  if (sources?.length) {
    where.source = { in: sources };
  }

  let orderBy: Prisma.AssetOrderByWithRelationInput = {};
  switch (sort) {
    case 'oldest':
      orderBy = { uploadedAt: 'asc' };
      break;
    case 'name':
      orderBy = { fileName: 'asc' };
      break;
    case 'newest':
    default:
      orderBy = { uploadedAt: 'desc' };
  }

  const total = await context.entities.Asset.count({ where });

  const assets = await context.entities.Asset.findMany({
    where,
    orderBy,
    skip: (page - 1) * limit,
    take: limit,
    include: {
      photographyModel: true,
      variations: {
        include: {
          photographyModel: true,
        },
      },
      originalAsset: {
        include: {
          photographyModel: true,
          variations: {
            include: {
              photographyModel: true,
            },
          },
        },
      },
      tags: true,
      collections: true,
      analytics: true,
    },
  });

  // Only include generated tasks for non-deleted assets
  const generatedAssets =
    filter !== 'deleted' ? await getGeneratedAssets(context, currentUser.id, args.organizationId) : [];

  return {
    assets: [...assets, ...generatedAssets],
    total: total + generatedAssets.length,
  };
};

// Helper function to get generated assets
async function getGeneratedAssets(context: any, userId: string, organizationId: string) {
  const generatedTasks = await context.entities.GenerationTask.findMany({
    where: {
      userId: userId,
      status: 'COMPLETED',
    },
  });

  return generatedTasks.flatMap((task: any) => {
    if (!task.result || !task.prompt) return [];
    const result = JSON.parse(task.result);
    const images = result.images || [];
    return images.map((imageUrl: string, index: number) =>
      context.entities.Asset.create({
        data: {
          id: parseInt(`${task.id}${index}`, 10),
          isGenerated: true,
          isVariation: false,
          fileUrl: imageUrl,
          fileName: `Generated Image ${index + 1}`,
          fileType: 'image/png',
          category: 'Generated',
          userId: task.userId,
          uploadedAt: task.createdAt,
          photographyModelId: task.modelId,
          prompt: task.prompt,
          deletedAt: null,
          generationTaskId: task.id,
          originalAssetId: null,
          variationSettings: Prisma.JsonNull,
          organizationId: organizationId,
        },
      })
    );
  });
}

// Get assets by tags
export const getAssetsByTags = async (
  args: {
    tagIds: string[];
    organizationId: string;
    limit?: number;
  },
  context: any
) => {
  const currentUser = authenticateUser(context);
  const { tagIds, organizationId, limit = 50 } = args;

  const assets = await context.entities.Asset.findMany({
    where: {
      organizationId,
      deletedAt: null,
      isCover: true,
      tags: {
        some: {
          id: { in: tagIds },
        },
      },
    },
    include: {
      tags: true,
      collections: true,
      analytics: true,
    },
    take: limit,
    orderBy: {
      uploadedAt: 'desc',
    },
  });

  return assets;
};

// Get assets by collection
export const getAssetsByCollection = async (
  args: {
    collectionId: string;
    page?: number;
    limit?: number;
  },
  context: any
) => {
  const currentUser = authenticateUser(context);
  const { collectionId, page = 1, limit = 20 } = args;

  // Verify user can access the collection
  const collection = await context.entities.AssetCollection.findFirst({
    where: {
      id: collectionId,
      OR: [{ userId: currentUser.id }, { isPublic: true }],
    },
  });

  if (!collection) {
    throw new HttpError(404, 'Collection not found or you do not have permission to access it');
  }

  const total = await context.entities.Asset.count({
    where: {
      collections: {
        some: { id: collectionId },
      },
      deletedAt: null,
    },
  });

  const assets = await context.entities.Asset.findMany({
    where: {
      collections: {
        some: { id: collectionId },
      },
      deletedAt: null,
    },
    include: {
      tags: true,
      collections: true,
      analytics: true,
    },
    skip: (page - 1) * limit,
    take: limit,
    orderBy: {
      uploadedAt: 'desc',
    },
  });

  return {
    assets,
    total,
    collection,
  };
};

// Search assets with advanced filters
export const searchAssets = async (
  args: {
    query?: string;
    organizationId: string;
    fileTypes?: string[];
    categories?: string[];
    dateRange?: {
      from: Date;
      to: Date;
    };
    sizeRange?: {
      min: number;
      max: number;
    };
    isGenerated?: boolean;
    page?: number;
    limit?: number;
  },
  context: any
) => {
  const currentUser = authenticateUser(context);
  const {
    query,
    organizationId,
    fileTypes,
    categories,
    dateRange,
    sizeRange,
    isGenerated,
    page = 1,
    limit = 20,
  } = args;

  const where: Prisma.AssetWhereInput = {
    organizationId,
    deletedAt: null,
    isCover: true,
  };

  // Text search
  if (query) {
    where.OR = [
      { fileName: { contains: query, mode: 'insensitive' } },
      { tags: { some: { name: { contains: query, mode: 'insensitive' } } } },
      { category: { contains: query, mode: 'insensitive' } },
    ];
  }

  // File type filter
  if (fileTypes?.length) {
    where.fileType = { in: fileTypes };
  }

  // Category filter
  if (categories?.length) {
    where.category = { in: categories };
  }

  // Date range filter
  if (dateRange) {
    where.uploadedAt = {
      gte: dateRange.from,
      lte: dateRange.to,
    };
  }

  // Generated filter
  if (typeof isGenerated === 'boolean') {
    where.isGenerated = isGenerated;
  }

  const total = await context.entities.Asset.count({ where });

  const assets = await context.entities.Asset.findMany({
    where,
    include: {
      tags: true,
      collections: true,
      analytics: true,
      photographyModel: true,
    },
    skip: (page - 1) * limit,
    take: limit,
    orderBy: {
      uploadedAt: 'desc',
    },
  });

  return {
    assets,
    total,
    page,
    limit,
    hasMore: total > page * limit,
  };
};
