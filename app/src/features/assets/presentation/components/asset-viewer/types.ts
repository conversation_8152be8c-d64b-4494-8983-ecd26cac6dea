import { Asset, AssetTag, User } from 'wasp/entities';

export interface Comment {
  id: string;
  text: string;
  x: number;
  y: number;
  createdAt: Date;
  author: {
    id: string;
    username: string | null;
    email: string | null;
    color?: string;
  };
  replies: Array<{
    id: string;
    text: string;
    createdAt: Date;
    author: {
      id: string;
      username: string | null;
      email: string | null;
      color?: string;
    };
  }>;
}

export interface Reply {
  id: string;
  text: string;
  createdAt: Date;
  author: {
    id: string;
    email: string | null;
    username: string | null;
    createdAt: Date;
    color: string;
    lastActiveTimestamp: Date;
    isAdmin: boolean;
    stripeId: string | null;
    hasPaid: boolean;
    paymentProcessorUserId: string | null;
    checkoutSessionId: string | null;
    subscriptionStatus: string | null;
    subscriptionPlan: string | null;
    sendNewsletter: boolean;
    datePaid: Date | null;
    credits: number;
    sendEmail: boolean;
    showOnboaording: boolean;
    availableSlots: number;
    referredBy: string | null;
  };
}

export enum Tab {
  View = 'view',
  Info = 'info',
  Versions = 'versions',
  Comments = 'comments',
  Boards = 'boards',
}

export interface AssetViewerProps {
  asset: Asset & {
    tags?: AssetTag[];
    versions?: Asset[];
    user?: User;
    parentAsset?:
    | (Asset & {
      versions: Asset[];
    })
    | null;
    BoardAsset: {
      board: {
        id: string;
        name: string;
        _count: {
          assets: number;
        };
        assets: {
          asset: Asset;
        }[];
      };
    }[];
    comments?: (Comment & {
      author: User;
      replies: (Reply & {
        author: User;
      })[];
    })[];
  };
  settings?: {
    allowComments?: boolean;
    allowDownload?: boolean;
    showComments?: boolean;
    showVersions?: boolean;
  };
  openShareModal?: () => void;
  handleCommentAction?: () => boolean;
}

export interface InfoSectionProps {
  asset: Asset & {
    tags?: AssetTag[];
    versions?: Asset[];
    user?: User;
    parentAsset?:
    | (Asset & {
      versions: Asset[];
    })
    | null;
  };
  isAdmin: boolean;
}

export interface BoardSectionProps {
  asset: Asset & {
    tags?: AssetTag[];
    versions?: Asset[];
    user?: User;
    parentAsset?:
    | (Asset & {
      versions: Asset[];
    })
    | null;
    BoardAsset?: {
      board: {
        id: string;
        name: string;
        _count: {
          assets: number;
        };
        assets: {
          asset: Asset;
        }[];
      };
    }[];
  };
  isAdmin: boolean;
}

export interface ViewSectionProps {
  asset: Asset & {
    fileUrl?: string;
    fileName?: string;
    fileType?: string;
    fileSize?: number;
    metadata?: Record<string, unknown>;
    organizationId?: string;
  };
  imageRef: React.RefObject<HTMLDivElement>;
  comments: Comment[];
  setComments: React.Dispatch<React.SetStateAction<Comment[]>>;
  activeComment: string | null;
  setActiveComment: React.Dispatch<React.SetStateAction<string | null>>;
  newCommentMode: boolean;
  setNewCommentMode: React.Dispatch<React.SetStateAction<boolean>>;
  newCommentPos: { x: number; y: number };
  setNewCommentPos: React.Dispatch<React.SetStateAction<{ x: number; y: number }>>;
  handleImageClick: (e: React.MouseEvent<HTMLDivElement>) => void;
  hoveredCommentId?: string | null;
  setHoveredCommentId?: React.Dispatch<React.SetStateAction<string | null>>;
}

export interface CommentSectionProps {
  asset: Asset & {
    tags?: AssetTag[];
    versions?: Asset[];
    user?: User;
    parentAsset?:
    | (Asset & {
      versions: Asset[];
    })
    | null;
    BoardAsset?: {
      board: {
        id: string;
        name: string;
        _count: {
          assets: number;
        };
        assets: {
          asset: Asset;
        }[];
      };
    }[];
    comments?: Comment[];
  };
  isAdmin: boolean;
  hoveredCommentId?: string | null;
  setHoveredCommentId?: React.Dispatch<React.SetStateAction<string | null>>;
  handleCommentAction?: () => boolean;
}

export interface VersionSectionProps {
  versions: Asset[];
  isAdmin: boolean;
}