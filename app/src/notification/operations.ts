import { Notification } from 'wasp/entities';
import { HttpError } from 'wasp/server';
import { authenticateUser } from '../server/helpers';
import { getEmitter } from '../websocket';

export type GetUnreadNotificationsResult = {
  notifications: Notification[];
  totalUnread: number;
};

export async function getUnreadNotifications(args: void, context: any): Promise<GetUnreadNotificationsResult> {
  const user = authenticateUser(context);

  const notifications = await context.entities.Notification.findMany({
    where: {
      userId: user.id,
      isDismissed: false,
      OR: [{ expiresAt: null }, { expiresAt: { gt: new Date() } }],
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  const totalUnread = notifications.filter((n: Notification) => !n.isRead).length;

  return {
    notifications,
    totalUnread,
  };
}

export async function markNotificationAsRead(notificationId: string, context: any): Promise<boolean> {
  const user = authenticateUser(context);

  await context.entities.Notification.update({
    where: {
      id: notificationId,
      userId: user.id,
    },
    data: {
      isRead: true,
    },
  });

  return true;
}

export async function markAllNotificationsAsRead(args: void, context: any): Promise<boolean> {
  const user = authenticateUser(context);

  await context.entities.Notification.updateMany({
    where: {
      userId: user.id,
      isRead: false,
    },
    data: {
      isRead: true,
    },
  });

  return true;
}

export async function createNotification(
  data: {
    userId: string;
    type: string;
    subtype?: string;
    title: string;
    message: string;
    linkType?: string;
    linkId?: number;
    linkData?: any;
    imageUrl?: string;
  },
  context: any
): Promise<Notification> {
  const user = authenticateUser(context);

  const notification = await context.entities.Notification.create({
    data: {
      userId: data.userId,
      type: data.type,
      subtype: data.subtype,
      title: data.title,
      message: data.message,
      linkType: data.linkType,
      linkId: data.linkId,
      linkData: data.linkData,
      imageUrl: data.imageUrl,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    },
  });

  // Emit to the user's room
  try {
    const emitter = getEmitter();
    // Use a custom emit function for notifications
    emitter.emitTaskUpdate(data.userId, {
      taskId: notification.id,
      status: 'NOTIFICATION',
      progress: 100,
      result: notification,
    });
  } catch (error) {
    console.warn('Socket.io emitter not available, notification not emitted:', error);
  }

  return notification;
}

export async function createNotificationFromTemplate(
  data: {
    userId: string;
    type: string;
    subtype?: string;
    templateData: Record<string, any>;
    linkType?: string;
    linkId?: number;
    linkData?: any;
  },
  context: any
): Promise<Notification> {
  const user = authenticateUser(context);

  const notification = await context.entities.Notification.create({
    data: {
      userId: data.userId,
      type: data.type,
      subtype: data.subtype,
      title: data.templateData.title,
      message: data.templateData.message,
      linkType: data.linkType,
      linkId: data.linkId,
      linkData: data.linkData,
      imageUrl: data.templateData.imageUrl,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    },
  });

  // Emit to the user's room
  try {
    const emitter = getEmitter();
    // Use a custom emit function for notifications
    emitter.emitTaskUpdate(data.userId, {
      taskId: notification.id,
      status: 'NOTIFICATION',
      progress: 100,
      result: notification,
    });
  } catch (error) {
    console.warn('Socket.io emitter not available, notification not emitted:', error);
  }

  return notification;
}
