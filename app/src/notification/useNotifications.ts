import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from 'wasp/client/auth';
import { useSocketListener } from 'wasp/client/webSocket';
import { toast } from 'react-hot-toast';

import { NOTIFICATION_TYPES, NOTIFICATION_SUBTYPES } from './constants';
import {
  createNotification,
  createNotificationFromTemplate,
  getUnreadNotifications,
  markAllNotificationsAsRead,
  markNotificationAsRead,
} from 'wasp/client/operations';

export interface Notification {
  id: string;
  userId: string;
  type: string;
  subtype: string | null;
  title: string;
  message: string;
  linkType: string | null;
  linkId: number | null;
  linkData: any;
  imageUrl: string | null;
  isRead: boolean;
  isDismissed: boolean;
  createdAt: string;
  expiresAt: string | null;
}

export interface UseNotificationsResult {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  refresh: () => Promise<void>;
  sendNotification: (params: {
    userId: string;
    type: string;
    subtype?: string;
    title: string;
    message: string;
    linkType?: string;
    linkId?: number;
    linkData?: any;
    imageUrl?: string;
  }) => Promise<void>;
  sendCommentNotification: (params: {
    userId: string;
    actorName: string;
    commentText: string;
    assetId?: number;
  }) => Promise<void>;
}

/**
 * Hook for managing notifications in a component
 */
export function useNotifications(): UseNotificationsResult {
  const { data: user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const shownNotifications = useRef<Set<string>>(new Set());

  // Fetch notifications on component mount if user is logged in
  useEffect(() => {
    if (user) {
      fetchNotifications();
    }
  }, [user]);

  // Listen for new notifications via WebSocket
  useSocketListener('notification:new', (notification: Notification) => {
    // Add to state if it's not already there
    setNotifications((prev) => {
      if (prev.some((n) => n.id === notification.id)) {
        return prev;
      }
      return [notification, ...prev];
    });

    setUnreadCount((prev) => prev + 1);
  });

  const fetchNotifications = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const result = await getUnreadNotifications();

      // Convert Date objects to strings for UI display
      const notificationsWithStringDates = result.notifications.map((n: any) => ({
        ...n,
        createdAt: new Date(n.createdAt).toISOString(),
        expiresAt: n.expiresAt ? new Date(n.expiresAt).toISOString() : null,
      }));

      // Update notifications state
      setNotifications(notificationsWithStringDates);

      // Update unread count
      setUnreadCount(result.totalUnread);

      // Add all notification IDs to shown set
      notificationsWithStringDates.forEach((n: Notification) => {
        shownNotifications.current.add(n.id);
      });
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      await markNotificationAsRead(notificationId);

      // Update local state
      setNotifications((prev) => prev.map((n) => (n.id === notificationId ? { ...n, isRead: true } : n)));

      // Update unread count
      setUnreadCount((prev) => Math.max(0, prev - 1));
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      await markAllNotificationsAsRead();
      // Update UI
      setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));
      setUnreadCount(0);
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }, []);

  // Function to send a custom notification
  const sendNotification = useCallback(
    async (params: {
      userId: string;
      type: string;
      subtype?: string;
      title: string;
      message: string;
      linkType?: string;
      linkId?: number;
      linkData?: any;
      imageUrl?: string;
    }) => {
      try {
        // Type cast to work around Wasp-generated type mismatch
        await createNotification(params as any);
      } catch (error) {
        console.error('Failed to send notification:', error);
      }
    },
    []
  );

  // Specialized function to send a comment notification
  const sendCommentNotification = useCallback(
    async (params: { userId: string; actorName: string; commentText: string; assetId?: number }) => {
      try {
        // Type cast to work around Wasp-generated type mismatch
        await createNotificationFromTemplate({
          userId: params.userId,
          type: NOTIFICATION_TYPES.ACTIVITY,
          subtype: NOTIFICATION_SUBTYPES.COMMENT,
          templateData: {
            actorName: params.actorName,
            commentText: params.commentText,
          },
          linkType: 'asset',
          linkId: params.assetId ? String(params.assetId) : undefined,
        } as any);
      } catch (error) {
        console.error('Failed to send comment notification:', error);
      }
    },
    []
  );

  return {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    refresh: fetchNotifications,
    sendNotification,
    sendCommentNotification,
  };
}

// Export notification types and subtypes for easy access
export { NOTIFICATION_TYPES, NOTIFICATION_SUBTYPES };
