import React, { createContext, useContext } from 'react';
import { useSocketListener } from 'wasp/client/webSocket';
import { useNotifications, Notification } from './useNotifications';

// Create a context to provide notification data and methods
interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isLoading: boolean;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  refresh: () => Promise<void>;
  sendCommentNotification: (params: {
    userId: string;
    actorName: string;
    commentText: string;
    assetId?: number;
  }) => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | null>(null);

export const useNotificationContext = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
};

const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Use our custom hook to get all notification functionality
  const notificationData = useNotifications();

  return <NotificationContext.Provider value={notificationData}>{children}</NotificationContext.Provider>;
};

export default NotificationProvider;
