import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Label, Input, Alert, AlertDescription, CardFooter, Button } from '../../components/ui/index';
import { signup } from 'wasp/client/auth';
import { Link } from 'react-router-dom';
import { Checkbox } from '../../components/ui/checkbox';

const registerSchema = z
  .object({
    email: z.string().min(1, { message: 'Email is required' }).email({ message: 'Invalid email address' }),
    password: z
      .string()
      .min(1, { message: 'Password is required' })
      .min(6, { message: 'Password must be at least 6 characters long' }),
    confirmPassword: z.string().min(1, { message: 'Confirm password is required' }),
    referralCode: z.string().optional(),
    terms: z.string({ required_error: 'You must agree to the Terms of Service and Privacy Policy' }),
    marketing: z.string().optional(),
  })
  .refine((data) => data.confirmPassword === data.password, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });

type RegisterFormValues = z.infer<typeof registerSchema>;

export default function RegisterForm({ email, callback }: { email?: string; callback: () => void }) {
  const [error, setError] = useState<string | null>(null);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormValues>({
    mode: 'onChange',
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email,
    },
  });

  console.log({ errors });

  const onSubmit = async (data: RegisterFormValues) => {
    try {
      // Here you would typically send a request to your registration API
      console.log('Registration attempt', data);
      // Simulate an API call

      await signup({
        email: data.email,
        password: data.password,
        ...(data.referralCode && { referralCode: data.referralCode }),
      });

      await callback();
      // If registration is successful, you might redirect the user or update the app state
      console.log('Registration successful');
      setError(null);
    } catch (err: unknown) {
      console.error('Registration error:', err);
      setError('Failed to register. Please try again.');
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className='grid w-full items-center gap-4'>
        <div className='flex flex-col space-y-1.5'>
          <Label htmlFor='email'>Email</Label>
          <Input id='email' disabled={!!email} type='email' placeholder='Enter your email' {...register('email')} />
          {errors.email && <span className='text-red-500 text-sm'>{errors.email.message}</span>}
        </div>
        <div className='flex flex-col space-y-1.5'>
          <Label htmlFor='password'>Password</Label>
          <Input id='password' type='password' placeholder='Enter your password' {...register('password')} />
          <p className='text-xs text-muted-foreground'>Must be at least 6 characters long</p>
          {errors.password && <span className='text-red-500 text-sm'>{errors.password.message}</span>}
        </div>
        <div className='flex flex-col space-y-1.5'>
          <Label htmlFor='confirmPassword'>Confirm Password</Label>
          <Input
            id='confirmPassword'
            type='password'
            placeholder='Confirm your password'
            {...register('confirmPassword')}
          />
          {errors.confirmPassword && <span className='text-red-500 text-sm'>{errors.confirmPassword.message}</span>}
        </div>
        <div className='space-y-2'>
          <Label htmlFor='referralCode'>Referral code (optional)</Label>
          <Input id='referralCode' placeholder='Enter referral code' {...register('referralCode')} />
          <p className='text-xs text-muted-foreground'>
            Have a referral code? Enter it here to unlock special benefits
          </p>
        </div>
        <div className='flex items-start space-x-2'>
          <Checkbox id='terms' className='mt-1' {...register('terms')} />
          <Label htmlFor='terms' className='text-sm font-normal cursor-pointer leading-5'>
            I agree to the{' '}
            <Link to='/terms' className='text-primary hover:underline'>
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link to='/privacy' className='text-primary hover:underline'>
              Privacy Policy
            </Link>
          </Label>
        </div>
        {errors.terms && (
          <span className='text-red-500 text-sm'>You must agree to the Terms of Service and Privacy Policy</span>
        )}
        <div className='flex items-start space-x-2'>
          <Checkbox id='marketing' className='mt-1' {...register('marketing')} />
          <Label htmlFor='marketing' className='text-sm font-normal cursor-pointer leading-5'>
            I would like to receive product updates and marketing emails
          </Label>
        </div>
      </div>
      {error && (
        <Alert variant='destructive' className='mt-4'>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      <CardFooter className='flex justify-between mt-4 px-0'>
        <Button
          type='submit'
          className='w-full dark:bg-primary bg-primary dark:hover:bg-olive-400 hover:bg-olive-400 text-white'
        >
          Sign up
        </Button>
      </CardFooter>
    </form>
  );
}
