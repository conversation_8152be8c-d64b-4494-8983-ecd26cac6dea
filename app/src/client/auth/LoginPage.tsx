import React, { useEffect } from 'react';
import { useAuth, GoogleSignInButton } from 'wasp/client/auth';
import { useNavigate } from 'react-router-dom';
import { Link } from 'react-router-dom';
import { AuthWrapper } from './authWrapper';
import { handlePostLoginRedirect } from '../../features/canvas/presentation/utils/shared-canvas-redirect';

import { Separator } from '../components/ui/separator';
import LoginForm from './components/login-form';

export default function Login() {
  const { data: user } = useAuth();

  const navigate = useNavigate();

  async function onSuccessCallback() {
    // Check for shared canvas redirect first (shared canvas links override everything)
    const hasRedirect = handlePostLoginRedirect(navigate);
    if (hasRedirect) {
      return;
    }
  }

  useEffect(() => {
    if (!user) {
      return;
    }

    // If the URL already contains a redirect (e.g. shared canvas) the helper
    // will handle it and we exit early.
    const hasRedirect = handlePostLoginRedirect(navigate);
    if (hasRedirect) {
      return;
    }

    navigate('/dashboard');
  }, [user, navigate]);

  return (
    <AuthWrapper>
      <div className='my-10 bg-white dark:bg-gray-800'>
        <div className='space-y-1 text-center mb-10'>
          <h2 className='text-2xl font-bold'>Welcome back</h2>
          <p className='text-muted-foreground'>Sign in to your account to continue</p>
        </div>

        <div className='grid grid-cols-1 gap-4 mb-5'>
          <GoogleSignInButton />
        </div>

        <div className='relative mb-5'>
          <div className='absolute inset-0 flex items-center'>
            <Separator className='w-full' />
          </div>
          <div className='relative flex justify-center text-xs uppercase'>
            <span className='bg-background px-2 text-muted-foreground'>Or continue with email</span>
          </div>
        </div>

        <div className='space-y-4'>
          <LoginForm callback={onSuccessCallback} />
        </div>

        <div className='flex flex-col space-y-4 mt-6'>
          <div className='text-sm text-center text-muted-foreground'>
            {"Don't have an account? "}
            <Link to='/signup' className='text-primary hover:underline font-medium'>
              Sign up
            </Link>
          </div>

          <div className='text-xs text-center text-muted-foreground'>
            By signing in, you agree to our{' '}
            <Link to='/terms' className='hover:underline'>
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link to='/privacy' className='hover:underline'>
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </AuthWrapper>
  );
}
