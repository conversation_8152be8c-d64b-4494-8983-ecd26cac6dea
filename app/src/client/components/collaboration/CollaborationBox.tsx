import React, { useState, useRef, useEffect, memo } from 'react';
import { useAuth } from 'wasp/client/auth';
import { useCanvasSync } from '../../../features/canvas/presentation/hooks/useCanvasSync';
import { Avatar, AvatarFallback } from '../ui/avatar';
import { cn } from '../../lib/utils';
import { ChevronDown, Users } from 'lucide-react';

interface CollaborationBoxProps {
  className?: string;
  canvasId?: string;
}

export const CollaborationBox: React.FC<CollaborationBoxProps> = memo(({ className, canvasId }) => {
  const { data: user } = useAuth();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLButtonElement>(null);

  // Use the unified Cloudflare Workers system for user presence
  const { users } = useCanvasSync({
    roomId: canvasId || 'default-room'
  });

  // Convert Cloudflare Workers users to the format expected by this component
  const activeUsers = users.map(u => ({
    userId: u.user.id,
    username: u.user.name,
  }));

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        triggerRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Don't show if no active users or only current user
  const otherUsers = activeUsers.filter((activeUser) => activeUser.userId !== user?.id);

  // Debug logging
  // console.log('[CollaborationBox] Debug:', {
  //   activeUsers,
  //   currentUserId: user?.id,
  //   otherUsers,
  //   otherUsersLength: otherUsers.length
  // });

  // Temporarily always show for testing - remove this condition later
  // if (otherUsers.length === 0) return null;

  const visibleUsers = otherUsers.slice(0, 3);
  const remainingCount = otherUsers.length - 3;

  return (
    <div className={cn('relative', className)}>
      {/* Main collaboration button */}
      <button
        ref={triggerRef}
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className='flex items-center gap-2 bg-[#F9F7ED] border border-[#E8E4D4] rounded-lg px-3 py-2 shadow-sm hover:shadow-md transition-all duration-200'
        style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.08)' }}
        aria-label='Show active collaborators'
      >
        {/* Avatar stack */}
        <div className='flex -space-x-2'>
          {otherUsers.length > 0 ? (
            <>
              {visibleUsers.map((activeUser, idx) => (
                <Avatar key={`${activeUser.userId}-${idx}`} className='h-6 w-6 border-2 border-white ring-0'>
                  <AvatarFallback className='text-xs bg-[#566B46] text-white'>
                    {activeUser.username
                      ?.split(' ')
                      .map((n) => n[0])
                      .join('')
                      .toUpperCase() || '?'}
                  </AvatarFallback>
                </Avatar>
              ))}
              {remainingCount > 0 && (
                <Avatar className='h-6 w-6 border-2 border-white bg-[#566B46] text-white ring-0'>
                  <AvatarFallback className='text-xs'>+{remainingCount}</AvatarFallback>
                </Avatar>
              )}
            </>
          ) : (
            <Avatar className='h-6 w-6 border-2 border-white ring-0'>
              <AvatarFallback className='text-xs bg-[#566B46] text-white'>
                {user?.username
                  ?.split(' ')
                  .map((n) => n[0])
                  .join('')
                  .toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
          )}
        </div>

        {/* User count and chevron */}
        <div className='flex items-center gap-1 text-[#566B46]'>
          <span className='text-sm font-medium'>{otherUsers.length}</span>
          <ChevronDown className={cn('w-4 h-4 transition-transform duration-200', isDropdownOpen && 'rotate-180')} />
        </div>
      </button>

      {/* Dropdown */}
      {isDropdownOpen && (
        <div
          ref={dropdownRef}
          className='absolute top-full mt-2 left-0 bg-[#F9F7ED] border border-[#E8E4D4] rounded-lg shadow-lg z-50 min-w-48'
          style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.08)' }}
        >
          <div className='p-3'>
            <div className='flex items-center gap-2 mb-3 text-[#566B46]'>
              <Users className='w-4 h-4' />
              <span className='text-sm font-medium'>Active on Canvas</span>
            </div>

            <div className='space-y-2'>
              {otherUsers.length > 0 ? (
                otherUsers.map((activeUser) => (
                  <div
                    key={activeUser.userId}
                    className='flex items-center gap-3 p-2 rounded-md hover:bg-[#566B46]/5 transition-colors'
                  >
                    <Avatar className='h-8 w-8 ring-0'>
                      <AvatarFallback className='text-sm bg-[#566B46] text-white'>
                        {activeUser.username
                          ?.split(' ')
                          .map((n) => n[0])
                          .join('')
                          .toUpperCase() || '?'}
                      </AvatarFallback>
                    </Avatar>
                    <div className='flex-1'>
                      <div className='text-sm font-medium text-[#566B46]'>{activeUser.username || 'Unknown User'}</div>
                      <div className='text-xs text-[#566B46]/70'>Active now</div>
                    </div>
                    <div className='w-2 h-2 bg-green-500 rounded-full'></div>
                  </div>
                ))
              ) : (
                <div className='flex items-center gap-3 p-2 rounded-md'>
                  <Avatar className='h-8 w-8 ring-0'>
                    <AvatarFallback className='text-sm bg-[#566B46] text-white'>
                      {user?.username
                        ?.split(' ')
                        .map((n) => n[0])
                        .join('')
                        .toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className='flex-1'>
                    <div className='text-sm font-medium text-[#566B46]'>{user?.username || 'You'}</div>
                    <div className='text-xs text-[#566B46]/70'>Working solo</div>
                  </div>
                  <div className='w-2 h-2 bg-blue-500 rounded-full'></div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
});
