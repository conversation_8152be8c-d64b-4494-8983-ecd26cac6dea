import { useState, useEffect } from 'react';

// Define the types for our reference data
export interface ReferenceItem {
  relativePath: string;
  url: string;
  category: string;
  brand?: string;
}

export interface ReferenceData {
  baseUrl: string;
  totalFiles: number;
  files: ReferenceItem[];
}

// Function to load the JSON data
export const loadReferenceData = async (): Promise<ReferenceData | null> => {
  try {
    const response = await fetch('/reference-data.json');
    if (!response.ok) {
      throw new Error(`Failed to load reference data: ${response.status} ${response.statusText}`);
    }

    // Parse the JSON data
    const data = await response.json();

    // Validate and clean the data
    if (data && data.files && Array.isArray(data.files)) {
      // Filter out any entries with missing required fields
      data.files = data.files.filter(
        (item: any) =>
          item &&
          typeof item.url === 'string' &&
          typeof item.relativePath === 'string' &&
          typeof item.category === 'string'
      );

      // Update the total files count
      data.totalFiles = data.files.length;
    }

    return data;
  } catch (error) {
    console.error('Error loading reference data:', error);
    return null;
  }
};

// Custom hook for loading and filtering reference data
export const useReferenceData = () => {
  const [data, setData] = useState<ReferenceData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Load the data on component mount
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const result = await loadReferenceData();
        setData(result);
        setError(null);
      } catch (err) {
        setError('Failed to load reference data');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Get unique categories and brands for filtering
  const categories = data?.files ? [...new Set(data.files.map((item) => item.category).filter(Boolean))] : [];

  const brands = data?.files
    ? [...new Set(data.files.map((item) => (item.brand ? item.brand.toLowerCase() : '')))]
        .filter(Boolean)
        .map((brand) => brand.charAt(0).toUpperCase() + brand.slice(1)) // Capitalize first letter
    : [];

  // Filter function
  const filterItems = (
    searchQuery: string = '',
    selectedCategory: string = '',
    selectedBrand: string = ''
  ): ReferenceItem[] => {
    if (!data?.files) return [];

    return data.files.filter((item) => {
      const matchesSearch =
        searchQuery === '' ||
        (item.relativePath && item.relativePath.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.category && item.category.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.brand && item.brand.toLowerCase().includes(searchQuery.toLowerCase()));

      const matchesCategory = selectedCategory === '' || item.category === selectedCategory;

      const matchesBrand =
        selectedBrand === '' || (item.brand && item.brand.toLowerCase() === selectedBrand.toLowerCase());

      return matchesSearch && matchesCategory && matchesBrand;
    });
  };

  // Paginated filter function for infinite scrolling
  const getPaginatedItems = (
    searchQuery: string = '',
    selectedCategory: string = '',
    selectedBrand: string = '',
    page: number = 1,
    itemsPerPage: number = 20
  ): {
    items: ReferenceItem[];
    totalItems: number;
    hasMore: boolean;
  } => {
    const filteredItems = filterItems(searchQuery, selectedCategory, selectedBrand);
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedItems = filteredItems.slice(startIndex, endIndex);

    return {
      items: paginatedItems,
      totalItems: filteredItems.length,
      hasMore: endIndex < filteredItems.length,
    };
  };

  return {
    data,
    isLoading,
    error,
    categories,
    brands,
    filterItems,
    getPaginatedItems,
  };
};
