import React, { useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import Select from 'react-select';
import { FEATURE_FLAG_OPTIONS } from '../constants/featureFlags';

// Type for user options
interface UserOption {
  value: string;
  label: string;
}

// Zod schema with conditional allowedUsers
const featureFlagSchema = z.object({
  key: z.string().min(1, 'Key is required'),
  name: z.string().min(3, 'Name must be at least 3 characters'),
  description: z.string().min(5, 'Description must be at least 5 characters').max(500, 'Description must not exceed 500 characters'),
  status: z.enum(['active', 'inactive'], {
    required_error: 'Status is required',
  }),
  targetType: z.enum(['public', 'specific'], {
    required_error: 'Target type is required',
  }),
  allowedUsers: z.array(z.string()).optional(),
});

type FeatureFlagFormData = z.infer<typeof featureFlagSchema>;

interface FeatureFlagFormProps {
  onSubmit: (data: FeatureFlagFormData) => Promise<void>;
  users?: UserOption[];
}

const FeatureFlagForm: React.FC<FeatureFlagFormProps> = ({ onSubmit, users }) => {
  const hasNoUsers = users?.length;
  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FeatureFlagFormData>({
    resolver: zodResolver(featureFlagSchema),
    defaultValues: {
      name: '',
      description: '',
      status: 'inactive',
      targetType: 'public',
      allowedUsers: [],
    },
  });

  const targetType = watch('targetType');

  const handleFormSubmit = async (data: FeatureFlagFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <div className='rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark'>
      <div className='border-b border-stroke py-4 px-6.5 dark:border-strokedark'>
        <h3 className='font-medium text-black dark:text-white'>Add Feature Flag</h3>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} noValidate>
        <div className='p-6.5'>
          <div className='mb-4.5'>
            <label className='mb-2.5 block text-black dark:text-white'>
              Feature Flag <span className='text-meta-1'>*</span>
            </label>
            <Controller
              name='key'
              control={control}
              render={({ field }) => (
                <Select
                  options={FEATURE_FLAG_OPTIONS}
                  value={FEATURE_FLAG_OPTIONS.find(option => option.value === field.value)}
                  onChange={(selected) => {
                    if (selected) {
                      field.onChange(selected.value);
                      setValue('name', selected.label);
                      setValue('description', selected.description);
                    }
                  }}
                  className='react-select-container'
                  classNamePrefix='react-select'
                  placeholder='Select feature flag...'
                />
              )}
            />
            {errors.key && <span className='text-meta-1 text-sm mt-1'>{errors.key.message}</span>}
          </div>

          <div className='mb-4.5'>
            <label className='mb-2.5 block text-black dark:text-white'>
              Name <span className='text-meta-1'>*</span>
            </label>
            <input
              type='text'
              {...register('name')}
              readOnly
              className='w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary'
            />
            {errors.name && <span className='text-meta-1 text-sm mt-1'>{errors.name.message}</span>}
          </div>

          <div className='mb-4.5'>
            <label className='mb-2.5 block text-black dark:text-white'>
              Description <span className='text-meta-1'>*</span>
            </label>
            <textarea
              rows={4}
              {...register('description')}
              readOnly
              className='w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary'
            />
            {errors.description && <span className='text-meta-1 text-sm mt-1'>{errors.description.message}</span>}
          </div>

          <div className='mb-4.5'>
            <label className='mb-2.5 block text-black dark:text-white'>
              Status <span className='text-meta-1'>*</span>
            </label>
            <select
              {...register('status')}
              className='w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 font-medium outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary'
            >
              <option value='inactive'>Inactive</option>
              <option value='active'>Active</option>
            </select>
            {errors.status && <span className='text-meta-1 text-sm mt-1'>{errors.status.message}</span>}
          </div>

          {hasNoUsers && (
            <>
              <div className='mb-4.5'>
                <label className='mb-2.5 block text-black dark:text-white'>
                  Target Type <span className='text-meta-1'>*</span>
                </label>
                <div className='relative z-20 bg-transparent dark:bg-form-input'>
                  <select
                    {...register('targetType')}
                    className='relative z-20 w-full appearance-none rounded border border-stroke bg-transparent py-3 px-5 outline-none transition focus:border-primary active:border-primary dark:border-form-strokedark dark:bg-form-input dark:focus:border-primary'
                  >
                    <option value='public'>Public</option>
                    <option value='specific'>Specific</option>
                  </select>
                  <span className='absolute top-1/2 right-4 z-30 -translate-y-1/2'>
                    <svg
                      className='fill-current'
                      width='24'
                      height='24'
                      viewBox='0 0 24 24'
                      fill='none'
                      xmlns='http://www.w3.org/2000/svg'
                    >
                      <g opacity='0.8'>
                        <path
                          fillRule='evenodd'
                          clipRule='evenodd'
                          d='M5.29289 8.29289C5.68342 7.90237 6.31658 7.90237 6.70711 8.29289L12 13.5858L17.2929 8.29289C17.6834 7.90237 18.3166 7.90237 18.7071 8.29289C19.0976 8.68342 19.0976 9.31658 18.7071 9.70711L12.7071 15.7071C12.3166 16.0976 11.6834 16.0976 11.2929 15.7071L5.29289 9.70711C4.90237 9.31658 4.90237 8.68342 5.29289 8.29289Z'
                          fill=''
                        ></path>
                      </g>
                    </svg>
                  </span>
                </div>
                {errors.targetType && <span className='text-meta-1 text-sm mt-1'>{errors.targetType.message}</span>}
              </div>

              {targetType === 'specific' && (
                <div className='mb-4.5'>
                  <label className='mb-2.5 block text-black dark:text-white'>
                    Allowed Users <span className='text-meta-1'>*</span>
                  </label>
                  <Controller
                    name='allowedUsers'
                    control={control}
                    render={({ field }) => (
                      <Select
                        isMulti
                        options={users}
                        value={users?.filter((user) => field.value?.includes(user.value))}
                        onChange={(selectedOptions) => {
                          field.onChange(selectedOptions?.map((option) => option.value) || []);
                        }}
                        className='react-select-container'
                        classNamePrefix='react-select'
                        placeholder='Select users...'
                      />
                    )}
                  />
                  {errors.allowedUsers && (
                    <span className='text-meta-1 text-sm mt-1'>{errors.allowedUsers.message}</span>
                  )}
                </div>
              )}
            </>
          )}

          <button
            type='submit'
            disabled={isSubmitting}
            className='flex w-full justify-center rounded bg-primary p-3 font-medium text-gray hover:bg-opacity-90 disabled:bg-opacity-50'
          >
            {isSubmitting ? 'Submitting...' : 'Submit'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default FeatureFlagForm;
