import { google, Auth } from 'googleapis';
import { config, HttpError } from 'wasp/server';
import type { GetGoogleDriveFiles } from 'wasp/server/operations';
import { getGoogleRefreshToken } from '../../services/googleOAuthService';

/**
 * Creates and returns a Google OAuth2 client using Redis-stored tokens
 * @param userId The user ID to get the token for
 */
async function getOAuth2Client(userId: string): Promise<Auth.OAuth2Client> {
  const clientId = process.env.GOOGLE_CLIENT_ID;
  const clientSecret = process.env.GOOGLE_CLIENT_SECRET;
  const redirectUri = `${config.serverUrl}/api/google/oauth/callback`;

  if (!clientId || !clientSecret) {
    throw new Error(
      'Missing Google OAuth credentials. Please set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables.'
    );
  }

  const oauth2Client = new google.auth.OAuth2(clientId, clientSecret, redirectUri);

  const tokenData = await getGoogleRefreshToken(userId);

  if (tokenData) {
    oauth2Client.setCredentials({
      refresh_token: tokenData.refreshToken,
    });
    return oauth2Client;
  }

  return oauth2Client;
}

type GetFilesInput = {
  pageToken?: string;
  pageSize?: number;
  searchQuery?: string;
  // Note: fileType is no longer used as we only retrieve images
};

/**
 * Image file type returned from Google Drive API
 */
type FileType = {
  id: string;
  name: string;
  mimeType: string;
  iconLink?: string;
  thumbnailLink?: string;
  webViewLink?: string;
  createdTime?: string;
  modifiedTime?: string;
  size?: string;
  // Image-specific metadata
  imageMediaMetadata?: {
    width?: number;
    height?: number;
    rotation?: number;
    location?: {
      latitude?: number;
      longitude?: number;
      altitude?: number;
    };
    time?: string;
    cameraMake?: string;
    cameraModel?: string;
    exposureTime?: number;
    aperture?: number;
    flashUsed?: boolean;
    focalLength?: number;
    isoSpeed?: number;
    meteringMode?: string;
    sensor?: string;
    exposureMode?: string;
    colorSpace?: string;
    whiteBalance?: string;
    exposureBias?: number;
    maxApertureValue?: number;
    subjectDistance?: number;
    lens?: string;
  };
};

type GetFilesResponse = {
  files: FileType[];
  nextPageToken?: string;
};

/**
 * Retrieves images from Google Drive for the authenticated user
 * This function only returns image files (mimeType contains 'image/')
 */
export const getGoogleDriveFiles: GetGoogleDriveFiles<GetFilesInput, GetFilesResponse> = async (args, context) => {
  // Check if user is authenticated
  if (!context.user) {
    throw new HttpError(401, 'You must be logged in to access Google Drive files');
  }

  try {
    console.log('Starting Google Drive files fetch');

    // Get OAuth2 client with credentials from Redis or environment variables
    const oauth2Client = await getOAuth2Client(context.user.id);

    // Log authentication status
    if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
      console.log('Google Auth initialized with environment variables');
    } else {
      console.log('Missing Google OAuth credentials in environment variables');
    }

    const drive = google.drive({ version: 'v3', auth: oauth2Client });
    console.log('Google Drive client initialized');

    // Always filter for images only
    let query = "mimeType contains 'image/'";

    // Note: fileType parameter is ignored as we're only retrieving images

    // Add search query if provided
    if (args.searchQuery) {
      const searchTerm = `name contains '${args.searchQuery}'`;
      query = query ? `${query} and ${searchTerm}` : searchTerm;
    }

    // Add trashed=false to exclude trashed files
    const trashedFilter = 'trashed=false';
    query = query ? `${query} and ${trashedFilter}` : trashedFilter;

    console.log(`Executing Google Drive files.list with query: ${query}`);

    // List image files from Google Drive
    const response = await drive.files.list({
      q: query,
      pageSize: args.pageSize || 30,
      pageToken: args.pageToken,
      fields:
        'nextPageToken, files(id, name, mimeType, iconLink, thumbnailLink, webViewLink, imageMediaMetadata, createdTime, modifiedTime, size)',
      orderBy: 'modifiedTime desc',
      // Only using 'drive' space as we don't have permissions for 'photos'
      spaces: 'drive',
    });

    console.log(`Google Drive API returned ${response.data.files?.length || 0} files`);

    // Ensure nextPageToken is either a string or undefined (not null)
    const nextPageToken = response.data.nextPageToken || undefined;

    return {
      files: response.data.files as FileType[],
      nextPageToken,
    };
  } catch (error: any) {
    // More detailed error logging
    console.error('Error fetching Google Drive files:');
    console.error('Error message:', error.message);
    console.error('Error stack:', error.stack);

    if (error.response) {
      console.error('API Error Response:', error.response.data);
      console.error('API Error Status:', error.response.status);
    }

    // Provide a more specific error message based on the error type
    let errorMessage = error.message || 'Failed to fetch files from Google Drive';
    let statusCode = 500;

    // Check for specific error types
    if (error.message && error.message.includes('invalid_grant')) {
      errorMessage =
        'Google Drive authentication failed. Your refresh token may have expired. Please reauthorize the application.';
      statusCode = 401;
    } else if (error.message && error.message.includes('invalid_client')) {
      errorMessage =
        'Invalid Google OAuth client credentials. Please check your GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables.';
      statusCode = 401;
    } else if (error.message && error.message.includes('granted scopes do not give access')) {
      errorMessage =
        'The application does not have permission to access all requested Google resources. Please check the OAuth scopes in app/src/auth/google.ts.';
      statusCode = 403;
    }

    throw new HttpError(statusCode, errorMessage);
  }
};
