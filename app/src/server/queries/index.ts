import {
  type DailyStats,
  type PageViewSource,
  type Task,
  type File,
  type WireframeImage,
  type Project,
  type Audience,
  type PhotographyModel,
  type GenerationTask,
  type Asset,
} from 'wasp/entities';

import { HttpError } from 'wasp/server';

import {
  type GetDailyStats,
  type GetAllTasksByUser,
  type GetAllFilesByUser,
  type GetWireframeImageById,
  type GetUserProjects,
  type LoadEditorState,
  type GetAudiences,
  type GetAudience,
  type GetUploadFileSignedURLFromS3,
  type GetDownloadFileSignedURLFromS3,
  type GetProject,
  type GetPhotographyModels,
  type GetTrainingModels,
  type GetUserTasks,
  type GetImageGenerationTask,
  type GetModelAssets,
  type GetGenerationTasks,
  type GetGenerationHistory,
  GetPaginatedFeatureFlags,
} from 'wasp/server/operations';

import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { v4 as uuidv4 } from 'uuid';

import { BFLTrainingService } from '../training-services/ai/bfl-service';
import { RunPodTrainingService } from '../training-services/ai/runpod-training-service';
import { checkModelFileExists } from '../libs/runpod';

// export * from './assets'; // Removed re-export of deleted assets module
// export * from './chat'; // Moved to src/features/infrastructure/wasp/queries/chat
export * from './googleDrive';

type DailyStatsWithSources = DailyStats & {
  sources: PageViewSource[];
};

type DailyStatsValues = {
  dailyStats: DailyStatsWithSources;
  weeklyStats: DailyStatsWithSources[];
};

type GetUserProjectsArgs = {
  userId: string;
};

type GetUserProjectsResult = Project;

// Assuming you have these types defined somewhere in your project
type LoadEditorStateArgs = {
  userId: number;
  projectId: string;
};

const s3Client = new S3Client({
  region: 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_S3_IAM_ACCESS_KEY!,
    secretAccessKey: process.env.AWS_S3_IAM_SECRET_KEY!,
  },
});

type S3Upload = {
  fileType: string;
  userInfo: string;
};

export const getUploadFileSignedURLFromS3: GetUploadFileSignedURLFromS3<
  S3Upload,
  { uploadUrl: string; key: string }
> = async ({ fileType, userInfo }) => {
  const ex = fileType.split('/')[1];
  const Key = `${userInfo}/${uuidv4()}.${ex}`;
  const s3Params = {
    Bucket: process.env.AWS_S3_FILES_BUCKET,
    Key,
    ContentType: `${fileType}`,
  };
  const command = new PutObjectCommand(s3Params);
  const uploadUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 });
  return { uploadUrl, key: Key };
};

export const getDownloadFileSignedURLFromS3: GetDownloadFileSignedURLFromS3<{ key: string }, string> = async ({
  key,
}) => {
  const s3Params = {
    Bucket: process.env.AWS_S3_FILES_BUCKET,
    Key: key,
  };
  const command = new GetObjectCommand(s3Params);
  return await getSignedUrl(s3Client, command, { expiresIn: 3600 });
};

export const getUserTasks: GetUserTasks<void, GenerationTask[]> = async (_args, { user, entities }) => {
  if (!user) {
    throw new Error('User not authenticated');
  }

  const tasks = await entities.GenerationTask.findMany({
    where: { userId: user.id },
    orderBy: { createdAt: 'desc' },
  });

  return tasks;
};

export const getWireframeImageById: GetWireframeImageById<{ id: number }, WireframeImage | null> = async (
  { id },
  context
) => {
  authenticateUser(context);
  if (isNaN(id)) {
    throw new HttpError(400, 'Invalid wireframe image ID');
  }
  return context.entities.WireframeImage.findUnique({
    where: { id },
  });
};

// export const getBrandByLandingPageUrl: GetBrandByLandingPageUrl<{ landingPageUrl: string }, Brand | null> = async (
//   { landingPageUrl },
//   context
// ) => {
//   return context.entities.Brand.findUnique({
//     where: {
//       landingPageUrl,
//     },
//   });
// };

export const getUserProjects: GetUserProjects<GetUserProjectsArgs, GetUserProjectsResult[]> = async (args, context) => {
  return context.entities.Project.findMany({
    where: { userId: args.userId },
    orderBy: { createdAt: 'desc' },
  });
};

// Assuming ProjectEditorState is used to type the state argument

export const loadEditorState: LoadEditorState<LoadEditorStateArgs, string | null> = async (args, context) => {
  const authUser = authenticateUser(context);
  const { projectId } = args;

  // Try to find the ProjectEditorState for the given user and project
  const state = await context.entities.ProjectEditorState.findFirst({
    where: {
      AND: [{ userId: authUser.id }, { projectId: projectId }],
    },
  });

  console.log('Loaded state:', state); // Add this line to log the loaded state

  // Return the state if found, otherwise return null
  return state ? state.state : null;
};

// export const getBrands: GetBrands<void, Brand[]> = async (_, context) => {
//   return context.entities.Brand.findMany();
// };

export const getAllTasksByUser: GetAllTasksByUser<void, Task[]> = async (_args, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  return context.entities.Task.findMany({
    where: {
      user: {
        id: context.user.id,
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
};

export const getAllFilesByUser: GetAllFilesByUser<void, File[]> = async (_args, context) => {
  if (!context.user) {
    throw new HttpError(401);
  }
  return context.entities.File.findMany({
    where: {
      user: {
        id: context.user.id,
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
  });
};

export const getDailyStats: GetDailyStats<void, DailyStatsValues> = async (_args, context) => {
  if (!context.user?.isAdmin) {
    throw new HttpError(401);
  }
  const dailyStats = await context.entities.DailyStats.findFirstOrThrow({
    orderBy: {
      date: 'desc',
    },
    include: {
      sources: true,
    },
  });

  const weeklyStats = await context.entities.DailyStats.findMany({
    orderBy: {
      date: 'desc',
    },
    take: 7,
    include: {
      sources: true,
    },
  });

  return { dailyStats, weeklyStats };
};

export const getPaginatedFeatureFlags: GetPaginatedFeatureFlags<any, any> = async (args, context) => {
  // const filters = {status: args.status, keys: args.keys}

  const queryResults = await context.entities.FeatureFlag.findMany({
    skip: args.skip ?? 0,
    take: args.take ?? 10,
    ...(args.status && {
      where: {
        status: args.status,
      },
    }),
    orderBy: {
      id: 'desc',
    },
  });

  const totalFeatureFlagCount = await context.entities.FeatureFlag.count();
  const totalPages = Math.ceil(totalFeatureFlagCount / 10);

  return {
    featureFlags: queryResults,
    totalPages,
  };
};

export const getAudiences: GetAudiences<{ organizationId: string }, Audience[]> = async (
  { organizationId },
  context
) => {
  authenticateUser(context);

  return context.entities.Audience.findMany({
    where: { organizationId },
  });
};

export const getAudience: GetAudience<{ id: number }, Audience | null> = async (args, context) => {
  authenticateUser(context);

  return context.entities.Audience.findUnique({
    where: { id: args.id },
  });
};

type GetProjectArgs = {
  projectId: string;
};

export const getProject: GetProject<GetProjectArgs, Project> = async (args, context) => {
  const authUser = authenticateUser(context);

  const project = await context.entities.Project.findFirst({
    where: {
      id: args.projectId,
      userId: authUser.id,
    },
    include: {
      brandKit: true,
      audience: true,
      product: true,
    },
  });

  if (!project) {
    throw new HttpError(404, 'Project not found');
  }

  return project;
};

// For getting trained models (used in image generation)
export const getPhotographyModels: GetPhotographyModels<
  { organizationId: string; type?: 'product' | 'style' },
  PhotographyModel[]
> = async ({ organizationId, type }, context) => {
  authenticateUser(context);

  // Get all models that have either a BFL modelId or RunPod jobId
  // Logging removed to reduce noise

  // Logging and filtering code removed to reduce noise
  // Get models that are either:
  // 1. Have a modelId/runpodJobId
  // 2. Are in training state
  // 3. Were created in the last hour
  const models = await context.entities.PhotographyModel.findMany({
    where: {
      organizationId,
      ...(type && { type }), // Only include type filter if provided
      OR: [
        { modelId: { not: null } }, // BFL models
        { runpodJobId: { not: null } }, // RunPod models
        { status: { in: ['IN_QUEUE', 'IN_PROGRESS'] } }, // Models in training
        {
          createdAt: {
            gte: new Date(Date.now() - 60 * 60 * 1000), // Created in last hour
          },
        },
      ],
    },
    select: {
      id: true,
      name: true,
      status: true,
      usage: true,
      performance: true,
      createdAt: true,
      updatedAt: true,
      trainingStart: true,
      trainingEnd: true,
      imageUrl: true,
      progress: true,
      currentStep: true,
      totalSteps: true,
      estimatedTime: true,
      isTraining: true,
      userId: true,
      runpodJobId: true,
      productId: true,
      organizationId: true,
      provider: true,
      modelId: true,
      type: true,
    },
    orderBy: { createdAt: 'desc' },
  });

  // Check status for models that aren't ready
  const updatedModels = await Promise.all(
    models.map(async (model) => {
      // Skip if already marked as ready/completed
      if (model.status === 'READY' || model.status === 'COMPLETED') {
        return model;
      }

      try {
        let details;
        let updatedModel;

        // Skip status check for models that are already complete
        if (model.status === 'READY' || model.status === 'COMPLETED' || model.status === 'Trained') {
          return model;
        }

        if (model.provider === 'bfl' && model.modelId) {
          // Check status with BFL
          const bflService = new BFLTrainingService(process.env.BFL_API_KEY!);
          details = await bflService.getModelDetails(model.modelId);

          // Update model with BFL status
          updatedModel = await context.entities.PhotographyModel.update({
            where: { id: model.id },
            data: {
              status: details.status,
              isTraining: !['READY', 'COMPLETED'].includes(details.status),
              ...(details.status === 'READY' ? { trainingEnd: new Date() } : {}),
              progress: details.progress ? details.progress.toString() : null,
            },
          });
        } else if (model.provider === 'runpod' && model.runpodJobId) {
          // First check if model file exists in R2 for RunPod models
          const fileExists = await checkModelFileExists(model.id);
          if (fileExists) {
            // If file exists, mark model as complete
            updatedModel = await context.entities.PhotographyModel.update({
              where: { id: model.id },
              data: {
                status: 'Trained',
                isTraining: false,
                trainingEnd: new Date(),
                progress: '100',
                runpodJobId: null, // Clear runpodJobId since training is complete
              },
            });
            return updatedModel;
          }

          // If file doesn't exist, check status with RunPod
          const runpodService = new RunPodTrainingService(
            process.env.RUNPOD_API_KEY!,
            process.env.RUNPOD_TRAIN_MODEL_ENDPOINT!,
            JSON.stringify({
              accessId: process.env.AWS_S3_IAM_ACCESS_KEY,
              accessSecret: process.env.AWS_S3_IAM_SECRET_KEY,
              bucketName: process.env.AWS_S3_FILES_BUCKET,
              endpointUrl: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
            }),
            process.env.R2_PUBLIC_URL!
          );

          details = await runpodService.getFullModelStatus(model.id, model.runpodJobId);

          // Set initial values if they don't exist
          if (!model.currentStep && !model.totalSteps) {
            console.log(`Setting initial step values for model ${model.id}`);
            await context.entities.PhotographyModel.update({
              where: { id: model.id },
              data: {
                currentStep: 0,
                totalSteps: 100,
                progress: '0',
              },
            });
          }

          // Prepare update data, preserving existing values if new ones are undefined
          const updateData = {
            status: details.status,
            isTraining: details.isTraining,
            ...(details.status === 'READY' ? { trainingEnd: new Date() } : {}),
            // Only update progress if we have a new value, otherwise keep existing
            ...(details.progress !== undefined
              ? { progress: details.progress.toString() }
              : { progress: model.progress || '0' }),
            // Only update steps if we have new values, otherwise keep existing
            ...(details.currentStep !== undefined
              ? { currentStep: details.currentStep }
              : { currentStep: model.currentStep || 0 }),
            ...(details.totalSteps !== undefined
              ? { totalSteps: details.totalSteps }
              : { totalSteps: model.totalSteps || 100 }),
          };

          // Update model with RunPod status
          updatedModel = await context.entities.PhotographyModel.update({
            where: { id: model.id },
            data: updateData,
          });

          // Log the update for debugging
          console.log(`Model ${model.id} update details:`, {
            progress: {
              old: model.progress,
              new: updateData.progress,
              fromAPI: details.progress,
            },
            steps: {
              old: { current: model.currentStep, total: model.totalSteps },
              new: { current: updateData.currentStep, total: updateData.totalSteps },
              fromAPI: { current: details.currentStep, total: details.totalSteps },
            },
            isRedisError: details.progress === undefined && details.currentStep === undefined,
            isInitialSetup: !model.currentStep && !model.totalSteps,
          });
        }

        // Log the status update
        if (updatedModel) {
          console.log(`Model ${model.id} status updated:`, {
            provider: model.provider,
            oldStatus: model.status,
            newStatus: updatedModel.status,
            isTraining: updatedModel.isTraining,
            progress: updatedModel.progress,
          });
          return updatedModel;
        }
      } catch (error) {
        console.error(`Error checking status for model ${model.id}:`, error);
      }
      return model;
    })
  );

  // Logging removed to reduce noise
  return updatedModels;
};

export const getTrainingModels: GetTrainingModels<{ type?: 'product' | 'style' }, PhotographyModel[]> = async (
  args,
  context
) => {
  if (!context.user) {
    throw new HttpError(401);
  }

  // Get models that are still training
  const models = await context.entities.PhotographyModel.findMany({
    where: {
      userId: context.user.id,
      ...(args.type && { type: args.type }), // Only include type filter if provided
      OR: [
        { modelId: { not: null } }, // BFL models
        { runpodJobId: { not: null } }, // RunPod models
      ],
      status: { notIn: ['READY', 'COMPLETED'] },
    },
    select: {
      id: true,
      name: true,
      status: true,
      usage: true,
      performance: true,
      createdAt: true,
      updatedAt: true,
      trainingStart: true,
      trainingEnd: true,
      imageUrl: true,
      progress: true,
      currentStep: true,
      totalSteps: true,
      estimatedTime: true,
      isTraining: true,
      userId: true,
      runpodJobId: true,
      productId: true,
      organizationId: true,
      provider: true,
      modelId: true,
      type: true,
    },
    orderBy: { createdAt: 'desc' },
  });

  // Check status for each model
  const updatedModels = await Promise.all(
    models.map(async (model) => {
      try {
        let details;
        let updatedModel;

        // First check if model file exists in R2 for any model that's not marked as complete
        if (!['READY', 'COMPLETED', 'Trained'].includes(model.status)) {
          const fileExists = await checkModelFileExists(model.id);
          if (fileExists) {
            // If file exists, mark model as complete
            updatedModel = await context.entities.PhotographyModel.update({
              where: { id: model.id },
              data: {
                status: 'Trained',
                isTraining: false,
                trainingEnd: new Date(),
                progress: '100',
                ...(model.provider === 'runpod' ? { runpodJobId: null } : {}),
              },
            });
            return updatedModel;
          }
        }

        if (model.provider === 'bfl' && model.modelId) {
          // Check status with BFL
          const bflService = new BFLTrainingService(process.env.BFL_API_KEY!);
          details = await bflService.getModelDetails(model.modelId);

          // Update model with BFL status
          updatedModel = await context.entities.PhotographyModel.update({
            where: { id: model.id },
            data: {
              status: details.status,
              isTraining: !['READY', 'FAILED', 'COMPLETED'].includes(details.status),
              ...(details.status === 'READY' ? { trainingEnd: new Date() } : {}),
              progress: details.progress ? details.progress.toString() : null,
            },
          });
        } else if (model.provider === 'runpod' && model.runpodJobId) {
          // Check status with RunPod
          const runpodService = new RunPodTrainingService(
            process.env.RUNPOD_API_KEY!,
            process.env.RUNPOD_TRAIN_MODEL_ENDPOINT!,
            JSON.stringify({
              accessId: process.env.AWS_S3_IAM_ACCESS_KEY,
              accessSecret: process.env.AWS_S3_IAM_SECRET_KEY,
              bucketName: process.env.AWS_S3_FILES_BUCKET,
              endpointUrl: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
            }),
            process.env.R2_PUBLIC_URL!
          );

          details = await runpodService.getFullModelStatus(model.id, model.runpodJobId);

          // Set initial values if they don't exist
          if (!model.currentStep && !model.totalSteps) {
            console.log(`Setting initial step values for model ${model.id}`);
            await context.entities.PhotographyModel.update({
              where: { id: model.id },
              data: {
                currentStep: 0,
                totalSteps: 100,
                progress: '0',
              },
            });
          }

          // Prepare update data, preserving existing values if new ones are undefined
          const updateData = {
            status: details.status,
            isTraining: details.isTraining,
            ...(details.status === 'READY' ? { trainingEnd: new Date() } : {}),
            // Only update progress if we have a new value, otherwise keep existing
            ...(details.progress !== undefined
              ? { progress: details.progress.toString() }
              : { progress: model.progress || '0' }),
            // Only update steps if we have new values, otherwise keep existing
            ...(details.currentStep !== undefined
              ? { currentStep: details.currentStep }
              : { currentStep: model.currentStep || 0 }),
            ...(details.totalSteps !== undefined
              ? { totalSteps: details.totalSteps }
              : { totalSteps: model.totalSteps || 100 }),
          };

          // Update model with RunPod status
          updatedModel = await context.entities.PhotographyModel.update({
            where: { id: model.id },
            data: updateData,
          });

          // Log the update for debugging
          console.log(`Model ${model.id} update details:`, {
            progress: {
              old: model.progress,
              new: updateData.progress,
              fromAPI: details.progress,
            },
            steps: {
              old: { current: model.currentStep, total: model.totalSteps },
              new: { current: updateData.currentStep, total: updateData.totalSteps },
              fromAPI: { current: details.currentStep, total: details.totalSteps },
            },
            isRedisError: details.progress === undefined && details.currentStep === undefined,
            isInitialSetup: !model.currentStep && !model.totalSteps,
          });
        }

        // Log the status update
        if (updatedModel) {
          console.log(`Model ${model.id} status updated:`, {
            provider: model.provider,
            oldStatus: model.status,
            newStatus: updatedModel.status,
            isTraining: updatedModel.isTraining,
            progress: updatedModel.progress,
          });
          return updatedModel;
        }
      } catch (error) {
        console.error(`Error checking status for model ${model.id}:`, error);
      }
      return model;
    })
  );

  console.log(
    'Training models with current status:',
    updatedModels.map((m) => ({
      id: m.id,
      status: m.status,
      modelId: m.modelId,
      isTraining: m.isTraining,
      progress: m.progress,
    }))
  );

  return updatedModels;
};

export const getImageGenerationTask: GetImageGenerationTask<{ taskId: string }, GenerationTask> = async (
  { taskId },
  context
) => {
  const task = await context.entities.GenerationTask.findFirst({
    where: {
      taskId: taskId,
    },
    include: {
      assets: true,
    },
  });

  if (!task) {
    throw new HttpError(404, 'Task not found');
  }

  // Optional: If you want to keep some level of user-based access control
  if (context.user && task.userId && task.userId !== context.user.id) {
    throw new HttpError(403, 'Access denied');
  }

  return task;
};

export const getModelAssets: GetModelAssets<{ modelId: string }, Asset[]> = async ({ modelId }, context) => {
  const authUser = authenticateUser(context);

  const assets = await context.entities.Asset.findMany({
    where: {
      userId: authUser.id,
      generationTask: {
        modelId: modelId,
      },
    },
    orderBy: {
      uploadedAt: 'desc',
    },
  });

  return assets;
};

export const getGenerationTasks: GetGenerationTasks<{ modelId: string }, GenerationTask[]> = async (
  { modelId },
  context
) => {
  const authUser = authenticateUser(context);

  const tasks = await context.entities.GenerationTask.findMany({
    where: {
      userId: authUser.id,
      modelId: modelId,
    },
    orderBy: {
      createdAt: 'desc',
    },
    include: {
      assets: {
        where: {
          deletedAt: null, // Only include non-deleted assets
        },
        orderBy: {
          uploadedAt: 'desc',
        },
      },
    },
  });

  // Parse JSON strings if needed
  return tasks.map((task) => ({
    ...task,
    imageProgresses: task.imageProgresses ? JSON.parse(task.imageProgresses) : null,
    result: task.result ? JSON.parse(task.result) : null,
    expectedUrls: task.expectedUrls ? JSON.parse(task.expectedUrls) : null,
  }));
};

import { GenerationTaskWithAssets } from '../types/GenerationTaskWithAssets';
import { authenticateUser } from '../helpers';

export const getGenerationHistory: GetGenerationHistory<{ modelId: string }, GenerationTaskWithAssets[]> = async (
  { modelId },
  context
) => {
  if (!context.user) {
    throw new HttpError(401, 'Not authorized');
  }

  try {
    console.log(`[GetGenerationHistory] Fetching for user ${context.user.id}, modelId: ${modelId || 'none'}`);

    const modelFilter = modelId && modelId !== 'none' ? { modelId } : {}; // If no modelId or modelId is 'none', don't filter by modelId

    const tasks = await context.entities.GenerationTask.findMany({
      where: {
        userId: context.user.id,
        ...modelFilter,
        status: 'COMPLETED', // Only show completed tasks in history
      },
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        assets: {
          where: {
            deletedAt: null, // Only include non-deleted assets
          },
          orderBy: {
            uploadedAt: 'desc',
          },
        },
      },
    });

    console.log(`[GetGenerationHistory] Found ${tasks.length} completed tasks`);

    // Count total assets
    const totalAssets = tasks.reduce((sum, task) => sum + (task.assets?.length || 0), 0);
    console.log(`[GetGenerationHistory] Total assets: ${totalAssets}`);

    // Parse JSON fields if necessary
    const parsedTasks: GenerationTaskWithAssets[] = tasks.map((task) => {
      // Log the task for debugging
      console.log(
        `[GetGenerationHistory] Task ${task.id}: status=${task.status}, taskId=${task.taskId}, hasResult=${!!task.result}, hasAssets=${task.assets.length}`
      );

      return {
        ...task,
        imageProgresses: task.imageProgresses ? JSON.parse(task.imageProgresses) : null,
        result: task.result ? JSON.parse(task.result) : null,
        expectedUrls: task.expectedUrls ? JSON.parse(task.expectedUrls) : null,
      };
    });

    return parsedTasks;
  } catch (error) {
    console.error('[getGenerationHistory] Error:', error);
    throw error;
  }
};
