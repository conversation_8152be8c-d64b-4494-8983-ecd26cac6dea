/**
 * Canvas Emitter
 *
 * This file provides functions for emitting canvas updates to clients.
 * MIGRATED: Now uses Cloudflare Workers instead of WASP WebSocket
 */

import { emitCanvasElementAdd, emitCanvasElementUpdate, emitTaskUpdate, emitTaskComplete } from '../../utils/cloudflareWorkers';

/**
 * Safely emit an event to a user via Cloudflare Workers
 */
async function safeEmit(userId: string, event: string, data: any, roomId?: string): Promise<void> {
  try {
    // FAIL FAST: Require roomId - no fallbacks to prevent chaos
    if (!roomId) {
      const error = `[CanvasEmitter] ❌ FAIL FAST: roomId is required for ${event}. No fallbacks allowed.`;
      console.error(error);
      throw new Error(error);
    }

    console.log(`[CanvasEmitter] Emitting ${event} to user ${userId} in room ${roomId}`);

    // Use the appropriate Cloudflare Workers emitter method based on the event
    switch (event) {
      case 'canvas_element_add':
        await emitCanvasElementAdd(userId, roomId, data);
        break;
      case 'canvas_element_update':
        await emitCanvasElementUpdate(userId, roomId, data.elementId, data.content, data.taskId);
        break;
      case 'task_update':
        await emitTaskUpdate(userId, roomId, data.taskId, data.status, data.progress, data);
        break;
      case 'task_complete':
        await emitTaskComplete(userId, roomId, data.taskId, data.result);
        break;
      default:
        console.warn(`[CanvasEmitter] Unknown event type: ${event}`);
    }
  } catch (error) {
    console.error(`[CanvasEmitter] Error emitting ${event} to user ${userId}:`, error);
  }
}

/**
 * Emit a canvas update to a user
 */
export async function emitCanvasUpdate(userId: string, update: any, roomId?: string): Promise<void> {
  try {
    // Log the update
    console.log(`[CanvasEmitter] Emitting canvas update to user ${userId}:`, update.type);

    // Determine the event type based on the update type
    let eventType = 'canvas_element_update';

    // For task_created events, use canvas_element_add
    if (update.type === 'task_created') {
      eventType = 'canvas_element_add';
      console.log(`[CanvasEmitter] Using canvas_element_add for task_created event`);
    }

    // Emit via Cloudflare Workers
    await safeEmit(userId, eventType, {
      elementId: update.placeholderId,
      elementType: update.requestType || 'generic',
      position: { x: update.x || 100, y: update.y || 100 },
      size: { width: update.width || 400, height: update.height || 300 },
      content: {
        ...update,
        timestamp: new Date().toISOString(),
      },
      taskId: update.taskId
    }, roomId);

    // Log the emitted event
    console.log(`[CanvasEmitter] Emitted ${eventType} event to user ${userId}`);
  } catch (error) {
    console.error(`[CanvasEmitter] Error emitting canvas update to user ${userId}:`, error);
  }
}

/**
 * Emit a placeholder to a user
 */
export async function emitPlaceholder(userId: string, placeholderId: string, request: string, taskId?: string): Promise<void> {
  try {
    // Determine the request type
    const requestType = guessRequestType(request);

    // Create the placeholder content
    const content = {
      type: 'placeholder',
      requestType,
      request,
      status: 'pending',
      timestamp: new Date().toISOString(),
    };

    // Log the placeholder
    console.log(`[CanvasEmitter] Emitting placeholder to user ${userId}:`, placeholderId);

    // Add additional logging
    console.log(
      `[CanvasEmitter] Placeholder details: userId=${userId}, placeholderId=${placeholderId}, taskId=${taskId}, requestType=${requestType}`
    );

    // Emit via Cloudflare Workers
    await safeEmit(userId, 'canvas_element_add', {
      elementId: placeholderId,
      elementType: requestType === 'generate_image' ? 'image' : 'generic',
      position: { x: 100, y: 100 },
      size: { width: 400, height: 300 },
      content: {
        ...content,
        taskId, // Include the taskId in the content
        placeholderId, // Include the placeholderId in the content
      },
    });

    // Log the emitted event
    console.log(`[CanvasEmitter] Emitted canvas_element_add event to user ${userId}`);
  } catch (error) {
    console.error(`[CanvasEmitter] Error emitting placeholder to user ${userId}:`, error);
  }
}

// emitCanvasEvent function removed - now handled by conceptCardActions.ts using Cloudflare Workers

/**
 * Guess the request type based on the request text
 */
export function guessRequestType(request: string): string {
  const lowerRequest = request.toLowerCase();

  if (
    lowerRequest.includes('image') ||
    lowerRequest.includes('picture') ||
    lowerRequest.includes('photo') ||
    lowerRequest.includes('drawing')
  ) {
    return 'generate_image';
  }
  // If not an image, and generic HTML is removed, return a generic or unknown type.
  // This helps prevent accidental routing to old HTML logic.
  return 'unknown_request_type';
}
