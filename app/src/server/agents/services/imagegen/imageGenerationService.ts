/**
 * Image Generation Service
 *
 * This file provides a service for generating images.
 */

import { v4 as uuidv4 } from 'uuid';
import { getEmitter } from '../../../../websocket';
import { TaskStatus } from '../taskTypes';

// Define the image generation options
export interface ImageGenerationOptions {
  prompt: string;
  modelId?: string;
  finetuneId?: string;
  provider?: string;
  loras?: any[];
  numImages?: number;
  width?: number;
  height?: number;
  resolution?: string;
  input_image?: string;
  mode?: string;
  face_reference?: string;
  face_id_params?: any;
  mask_image?: string;
  inpaint_settings?: any;
  segment_description?: string;
  timestamp?: number;
  userId?: string;
  taskId?: string;
}

// Define the image generation result
export interface ImageGenerationResult {
  success: boolean;
  taskId?: string;
  error?: string;
  urls?: string[];
  width?: number;
  height?: number;
}

/**
 * Image Generation Service
 *
 * This class provides methods for generating images.
 */
export class ImageGenerationService {
  private static instances: Record<string, ImageGenerationService> = {};
  private provider: string;

  /**
   * Get an instance of the image generation service
   * @param provider The provider to use
   * @returns An instance of the image generation service
   */
  public static getInstance(provider: string = 'runpod'): ImageGenerationService {
    if (!this.instances[provider]) {
      this.instances[provider] = new ImageGenerationService(provider);
    }
    return this.instances[provider];
  }

  /**
   * Create a new image generation service
   * @param provider The provider to use
   */
  private constructor(provider: string) {
    this.provider = provider;
    console.log(`[ImageGenerationService] Created new instance for provider: ${provider}`);
  }

  /**
   * Generate an image
   * @param options The image generation options
   * @returns The image generation result
   */
  public async generateImage(options: ImageGenerationOptions): Promise<ImageGenerationResult> {
    const {
      prompt,
      modelId,
      finetuneId,
      provider,
      loras = [],
      numImages = 1,
      width,
      height,
      resolution,
      input_image,
      mode = 'text2img',
      face_reference,
      face_id_params,
      mask_image,
      inpaint_settings,
      segment_description,
      timestamp = Date.now(),
      userId,
      taskId = uuidv4(),
    } = options;

    console.log(`[ImageGenerationService] Generating image with provider: ${provider || this.provider}`);
    console.log(`[ImageGenerationService] Task ID: ${taskId}`);
    console.log(`[ImageGenerationService] Prompt: ${prompt.substring(0, 50)}...`);

    try {
      // Emit initial progress update
      if (userId) {
        this.emitProgressUpdate(userId, taskId, 10, TaskStatus.IN_PROGRESS);
      }

      // Simulate image generation
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Emit progress update
      if (userId) {
        this.emitProgressUpdate(userId, taskId, 50, TaskStatus.IN_PROGRESS);
      }

      // Simulate more processing
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // Generate placeholder URLs with the new gift placeholder image
      const urls = Array.from({ length: numImages }, (_, i) => `https://oliviatest.xyz/giftplaceholder.png`);

      // Emit completion update
      if (userId) {
        this.emitProgressUpdate(userId, taskId, 100, TaskStatus.COMPLETED, urls);
      }

      return {
        success: true,
        taskId,
        urls,
        width: width || 512,
        height: height || 512,
      };
    } catch (error) {
      console.error('[ImageGenerationService] Error generating image:', error);

      // Emit error update
      if (userId) {
        this.emitProgressUpdate(
          userId,
          taskId,
          0,
          TaskStatus.FAILED,
          undefined,
          error instanceof Error ? error.message : 'Unknown error'
        );
      }

      return {
        success: false,
        taskId,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Emit a progress update
   * @param userId The user ID
   * @param taskId The task ID
   * @param progress The progress (0-100)
   * @param status The task status
   * @param urls The image URLs
   * @param error The error message
   */
  private emitProgressUpdate(
    userId: number,
    taskId: string,
    progress: number,
    status: TaskStatus,
    urls?: string[],
    error?: string
  ): void {
    try {
      const emitter = getEmitter();

      // Emit task update
      emitter.emitTaskUpdate(userId, {
        taskId,
        status,
        progress,
        result: urls ? { urls } : undefined,
      });

      // Emit pending image progress
      emitter.emitUpdatePendingImageProgress(userId, {
        taskId,
        status,
        progress,
        imageUrl: urls && urls.length > 0 ? urls[0] : undefined,
      });

      // If completed, emit task complete
      if (status === TaskStatus.COMPLETED && urls) {
        emitter.emitTaskComplete(userId, {
          taskId,
          result: { urls },
        });
      }

      // If failed, emit task error
      if (status === TaskStatus.FAILED) {
        emitter.emitTaskError(userId, {
          taskId,
          error: error || 'Unknown error',
        });
      }
    } catch (error) {
      console.error('[ImageGenerationService] Error emitting progress update:', error);
    }
  }
}
