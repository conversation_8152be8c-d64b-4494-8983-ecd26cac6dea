/**
 * WebSocket Types
 *
 * This file defines the types for WebSocket events.
 */

import { Server, Socket } from 'socket.io';
import { WebSocketDefinition, WaspSocketData } from 'wasp/server/webSocket';
import {
  UpdateNewsletterCardStatusData,
} from '../../../websocket/types';

// Define WebSocket types
export type WebSocketFn = WebSocketDefinition<
  ClientToServerEvents,
  ServerToClientEvents,
  InterServerEvents,
  SocketData
>;

// Client to server events
export interface ClientToServerEvents {
  // Agent events
  start_agent: (data: { userId: number; initialMessage: string; metadata?: any }) => void;
  resume_agent: (data: { threadId: string; humanInput: string; metadata?: any }) => void;
  pause_agent: (data: { threadId: string; reason?: string }) => void;
  cancel_agent: (data: { threadId: string; reason?: string }) => void;

  // Legacy events - keep for backward compatibility
  startVoiceAgent: (data: { roomName?: string; systemPrompt?: string }) => void;
  stopVoiceAgent: (data: { roomName: string }) => void;
  chatMessage: (message: string) => void;
  setReferenceImages: (data: any) => void;
  screenshotTaken: (data: any) => void;
  importAudience: (data: any) => void;
  importProducts: (data: { url: string; brandName: string; region?: string }) => void;
  authenticate: (data: { userId: string }) => void;

  // Synthesis events
  join_synthesis: (data: { sessionId: string; userId: string }) => void;

  // Note: Canvas collaboration events removed - now handled by Cloudflare Workers
}

// Server to client events
export interface ServerToClientEvents {
  // Agent events
  agent_started: (data: { threadId: string; message: string }) => void;
  agent_resumed: (data: { threadId: string; message: string }) => void;
  agent_paused: (data: { threadId: string; message: string }) => void;
  agent_cancelled: (data: { threadId: string; message: string }) => void;
  agent_error: (data: { message: string; error?: string }) => void;
  agent_complete: (data: { threadId: string; message: string }) => void;
  update_progress: (data: { threadId: string; message: string; progress: number; error?: boolean }) => void;
  request_human_input: (data: { threadId: string; question: string; context: string; options: any }) => void;

  // Authentication events
  auth_ok: (data?: { userId: number; room: string }) => void;
  auth_error: (data: { message: string; error?: string }) => void;

  // Concept card events
  createConceptCard: (data: any) => void;
  updateConceptCard: (data: any) => void;

  // Legacy events - keep for backward compatibility
  voiceAgentStatus: (data: any) => void;
  voiceAgentTranscript: (data: any) => void;
  voiceAgentResponse: (data: any) => void;
  progressUpdate: (data: { progress: number; message?: string; stage?: string }) => void;
  result: (data: { products: any[]; metadata?: any }) => void;
  error: (data: { message: string; code?: string }) => void;

  // Synthesis events
  synthesis_progress: (data: any) => void;
  synthesis_result: (data: any) => void;
  synthesis_error: (data: any) => void;
  synthesis_joined: (data: { roomName: string; sessionId: string }) => void;
  synthesis_session_status: (data: any) => void;

  referenceImagesUpdated: (data: any) => void;
  voiceAgentCanvasUpdate: (data: any) => void;
  attachPendingImageToCanvas: (data: any) => void;
  canvasElementAdd: (data: any) => void;
  canvasElementRemove: (data: any) => void;
  canvasElementUpdate: (data: any) => void;
  chatMessage: (data: any) => void;
  chatToolCall: (data: any) => void;
  chatToolResult: (data: any) => void;
  'notification:new': (data: any) => void;
  takeScreenshot: (data: any) => void;
  updateNewsletterCardStatus: (data: UpdateNewsletterCardStatusData) => void;

  // Note: Canvas collaboration events removed - now handled by Cloudflare Workers
}

// Inter-server events
export interface InterServerEvents {}

// Socket data
export interface SocketData extends WaspSocketData {
  userId?: string; // Add userId property
}

export type IO = Server<ServerToClientEvents, ClientToServerEvents, InterServerEvents, SocketData>;
export type WSocket = Socket<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>;
