/**
 * 📦 Product Import Worker
 *
 * @description PG Boss worker for processing product imports in the background
 * @responsibility Handles product analysis, creation, and AI processing asynchronously
 */

import { type ProductImportJob } from 'wasp/server/jobs';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import sharp from 'sharp';
import axios from 'axios';
import OpenAI from 'openai';
import { productScraper } from '../../features/products/infrastructure/wasp/actions/productExtraction/services/productScraper';

// Cloudflare R2 Configuration
const r2Client = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_BUCKET_ACCESS_KEY!,
    secretAccessKey: process.env.R2_BUCKET_SECRET_KEY!,
  },
});

const getR2ImageUrl = (folderPath: string, fileName: string) =>
  `${process.env.R2_PUBLIC_URL}/${folderPath}/${fileName}`;

// Initialize <PERSON> for AI processing
const claude<PERSON><PERSON><PERSON> = new OpenAI({
  baseURL: 'https://openrouter.ai/api/v1',
  apiKey: process.env.OPENROUTER_API_KEY!,
  defaultHeaders: {
    'HTTP-Referer': 'https://olivia.ai',
    'X-Title': 'Olivia AI Product Import Worker',
  },
});

export interface ProductImportJobArgs {
  productId: number;
  userId: string;
  organizationId: string;
  productName: string;
  productUrl: string;
  initialData?: {
    brandName?: string;
    description?: string;
    price?: number;
    currency?: string;
    productType?: string;
    imageUrl?: string;
    sku?: string;
  };
  [key: string]:
    | string
    | number
    | boolean
    | null
    | undefined
    | { [key: string]: string | number | boolean | null | undefined };
}

// Helper function to ensure URL has protocol
function ensureProtocol(url: string): string {
  if (url.startsWith('//')) {
    return `https:${url}`;
  }
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return `https://${url}`;
  }
  return url;
}

// Helper function to download and upload image to R2
async function downloadAndUploadImage(imageUrl: string, productId: number, userId: number): Promise<string> {
  try {
    // Ensure URL has protocol
    const fullUrl = ensureProtocol(imageUrl);
    console.log('Processing image:', fullUrl);

    // Download image
    const response = await axios.get(fullUrl, {
      responseType: 'arraybuffer',
      maxRedirects: 5,
      timeout: 10000, // 10 second timeout
    });
    const buffer = Buffer.from(response.data);

    // Convert to JPG and optimize
    const jpgBuffer = await sharp(buffer).jpeg({ quality: 90 }).toBuffer();

    // Generate unique filename
    const uniqueFileName = `product-${Date.now()}-${Math.random().toString(36).substring(7)}.jpg`;
    const folderPath = `Products/${userId}/${productId}`;

    // Upload to R2
    const params = {
      Bucket: process.env.R2_BUCKET_NAME!,
      Key: `${folderPath}/${uniqueFileName}`,
      Body: jpgBuffer,
      ContentType: 'image/jpeg',
    };

    const command = new PutObjectCommand(params);
    await r2Client.send(command);

    // Return R2 URL
    return getR2ImageUrl(folderPath, uniqueFileName);
  } catch (error) {
    console.error('Failed to process image:', imageUrl, error);
    throw error;
  }
}

// Utility function to get product details without authentication
async function getProductDetailsWithoutAuth(
  productUrl: string,
  productName: string,
  productId: number,
  userId: string
) {
  try {
    // Get detailed product info using ProductScraper
    console.log(`[Product Details] Getting details for: ${productName}`);
    const productDetails = await productScraper.getProductDetails(productUrl, productName);

    // Process and upload images to R2
    const processedImages: Array<{ url: string; alt: string; selected: boolean }> = [];
    if (productDetails.images && productDetails.images.length > 0) {
      for (const img of productDetails.images) {
        try {
          const imageUrl = img.url;
          // Download and upload to R2
          const r2Url = await downloadAndUploadImage(imageUrl, productId, userId);

          processedImages.push({
            url: r2Url,
            alt: img.alt || productName,
            selected: false,
          });
        } catch (error) {
          console.error('Failed to process image:', error);
          // Continue with other images if one fails
        }
      }
    }

    return {
      product: {
        id: productId,
        name: productDetails.name,
        description: productDetails.description,
        imageUrl: processedImages[0]?.url || null,
        confidence: 0.85,
        price: productDetails.price || 0,
        currency: 'USD',
        sku: '',
        type: productDetails.type || 'Unknown',
        features: productDetails.features || [],
        selected: false,
        originalUrl: productUrl,
        images: processedImages,
        metadata: {
          ...productDetails,
          sourceUrl: productUrl,
          extractionTimestamp: new Date().toISOString(),
        },
      },
    };
  } catch (error) {
    console.error('Product Details Extraction Error:', error);
    throw new Error(`Failed to get product details: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Utility function to generate product brief without authentication
async function generateProductBriefWithoutAuth(
  productId: number,
  productName: string,
  description: string,
  type: string,
  features: string[]
) {
  try {
    const briefCompletion = await claudeHaiku.chat.completions.create({
      model: 'anthropic/claude-3-5-haiku-20241022:beta',
      messages: [
        {
          role: 'system',
          content:
            'You are an expert at creating comprehensive product briefs for marketing, sales, design, and photography teams. You excel at analyzing product details and creating detailed, actionable briefs that guide creative teams.',
        },
        {
          role: 'user',
          content: `Create a comprehensive product brief for ${productName} based on this information:

Product Name: ${productName}
Product Type: ${type}
Description: ${description}
Features: ${features.join(', ')}

Create a detailed brief that follows this exact structure:

{
    "objective": "Brief statement explaining the purpose of this document in guiding designers, copywriters, and marketers",
    "coreMessaging": {
        "productName": "Full product name",
        "tagline": "One compelling line that captures the product's essence",
        "positioningStatement": "Clear statement of what makes this product unique and valuable",
        "keyAttributes": {
            "functionalBenefits": ["List of practical benefits and advantages"],
            "technicalFeatures": ["List of technical specifications and capabilities"],
            "emotionalBenefits": ["List of emotional benefits and feelings"],
            "uniqueSellingProposition": "What makes this product stand out in the market"
        },
        "marketingPillars": ["3-4 core marketing messages that define the product's value"]
    },
    "visualBranding": {
        "packagingDesign": {
            "visualThemes": ["Key visual themes to incorporate in design"],
            "informationHierarchy": ["Order of information importance on packaging/materials"]
        },
        "photographyGuidance": {
            "mood": "Description of desired mood and atmosphere",
            "propsAndSetting": ["List of recommended props and settings"],
            "lighting": "Specific lighting requirements and style",
            "productStyling": ["How to style and present the product"]
        }
    },
    "copywriting": {
        "toneOfVoice": ["List of tone characteristics"],
        "heroBanner": "Compelling hero banner text for website/ads",
        "productDescription": "Detailed product description for marketing materials",
        "socialMediaTeasers": ["3-4 engaging social media post ideas"],
        "emailCampaign": {
            "subjectLines": ["3-4 attention-grabbing email subject lines"],
            "marketingSnippet": "Email marketing message that drives action"
        }
    },
    "salesAndMarketing": {
        "campaigns": [
            {
                "name": "Campaign name",
                "description": "Campaign description and objectives",
                "elements": ["List of campaign elements and tactics"]
            }
        ],
        "influencerStrategy": {
            "type": "Type of influencers to target",
            "goals": ["List of influencer campaign goals"],
            "contentIdeas": ["Content ideas for influencers"]
        }
    },
    "contentDeliverables": {
        "photography": {
            "studioShots": ["List of required studio product shots"],
            "lifestyleShots": ["List of required lifestyle/context shots"],
            "closeups": ["List of required detail/feature shots"]
        },
        "video": {
            "shortForm": ["List of short-form video content ideas"],
            "longForm": ["List of long-form video content ideas"]
        },
        "graphics": ["List of required graphic assets and visuals"]
    }
}

Ensure your response exactly matches this structure, replacing the example values with detailed, relevant content for ${productName}. Make the brief as comprehensive as possible, adapting the content to be appropriate for a ${type} product. Focus on creating actionable guidance for the creative team.`,
        },
      ],
      response_format: { type: 'json_object' },
    });

    return JSON.parse(briefCompletion.choices[0]?.message?.content || '{}');
  } catch (error) {
    console.error('Failed to generate product brief:', error);
    throw new Error(`Failed to generate product brief: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Utility function to generate AI product description without authentication
async function generateProductDescriptionWithoutAuth(
  productId: number,
  productName: string,
  description: string,
  type: string
) {
  try {
    const descriptionCompletion = await claudeHaiku.chat.completions.create({
      model: 'anthropic/claude-3-5-haiku-20241022:beta',
      messages: [
        {
          role: 'system',
          content:
            'You are an expert copywriter specializing in creating compelling product descriptions for e-commerce and marketing materials.',
        },
        {
          role: 'user',
          content: `Create a compelling, SEO-optimized product description for ${productName}.

Product Details:
- Name: ${productName}
- Type: ${type}
- Current Description: ${description}

Requirements:
1. Write a compelling, engaging description (150-200 words)
2. Include relevant keywords naturally
3. Focus on benefits and value proposition
4. Use persuasive language that drives conversions
5. Structure with clear sections (overview, features, benefits)
6. Include a call-to-action

Make it sound professional yet approachable, and ensure it's optimized for both search engines and human readers.`,
        },
      ],
      max_tokens: 500,
    });

    return descriptionCompletion.choices[0]?.message?.content || '';
  } catch (error) {
    console.error('Failed to generate product description:', error);
    throw new Error(
      `Failed to generate product description: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

/**
 * 🔄 Process Product Import Job
 * @description Background job to process a single product import with full analysis
 */
export const processProductImport: ProductImportJob<ProductImportJobArgs, void> = async (args, context) => {
  const { productId, userId, productName, productUrl, initialData } = args;

  console.log(`[ProductImportWorker] Starting import for product ${productId}: ${productName}`);

  try {
    // Check if product still exists before processing
    const existingProduct = await context.entities.Product.findUnique({
      where: { id: productId },
    });

    if (!existingProduct) {
      console.log(`[ProductImportWorker] Product ${productId} was deleted, skipping job gracefully`);
      return; // Exit gracefully without error to prevent retries
    }

    // Update product status to in progress
    await context.entities.Product.update({
      where: { id: productId },
      data: {
        analysisStatus: 'IN_PROGRESS',
        analysisStartedAt: new Date(),
        analysisError: null,
      },
    });

    // Step 1: Perform detailed product analysis
    console.log(`[ProductImportWorker] Analyzing product details for ${productName}`);
    const analysisResult = await getProductDetailsWithoutAuth(productUrl, productName, productId, userId);

    // Step 2: Generate product brief
    console.log(`[ProductImportWorker] Generating product brief for ${productName}`);
    const brief = await generateProductBriefWithoutAuth(
      productId,
      productName,
      analysisResult.product.description,
      analysisResult.product.type,
      analysisResult.product.features
    );

    // Step 3: Update product with detailed info and brief
    console.log(`[ProductImportWorker] Updating product with analysis results for ${productName}`);

    // Check if product still exists before updating
    const productBeforeUpdate = await context.entities.Product.findUnique({
      where: { id: productId },
    });

    if (!productBeforeUpdate) {
      console.log(`[ProductImportWorker] Product ${productId} was deleted during processing, stopping job gracefully`);
      return; // Exit gracefully without error
    }

    const updatedProduct = await context.entities.Product.update({
      where: { id: productId },
      data: {
        name: productName,
        brandName: initialData?.brandName || '',
        description: analysisResult.product.description,
        features: analysisResult.product.features,
        images: analysisResult.product.images.map((img: { url: string; alt: string; selected: boolean }) => img.url),
        price: analysisResult.product.price || initialData?.price || 0,
        currency: analysisResult.product.currency || initialData?.currency || 'USD',
        productType: analysisResult.product.type || initialData?.productType || 'Unknown',
        analysisStatus: 'COMPLETED',
        analysisCompletedAt: new Date(),
        reviews: JSON.stringify({ brief, briefGeneratedAt: new Date().toISOString() }),
        // Preserve original URL
        originalUrl: productUrl,
      },
    });

    // Step 4: Generate AI product description
    console.log(`[ProductImportWorker] Generating AI description for ${productName}`);
    try {
      const aiDescription = await generateProductDescriptionWithoutAuth(
        updatedProduct.id,
        productName,
        analysisResult.product.description,
        analysisResult.product.type
      );

      // Check if product still exists before final update
      const productBeforeFinalUpdate = await context.entities.Product.findUnique({
        where: { id: updatedProduct.id },
      });

      if (!productBeforeFinalUpdate) {
        console.log(
          `[ProductImportWorker] Product ${productId} was deleted before final update, skipping AI description update`
        );
        return; // Exit gracefully without error
      }

      // Update product with AI-generated description
      await context.entities.Product.update({
        where: { id: updatedProduct.id },
        data: {
          description: aiDescription,
        },
      });
    } catch (error) {
      console.error(`[ProductImportWorker] Failed to generate description for product ${productId}:`, error);
      // Don't fail the entire import if description generation fails
    }

    console.log(`[ProductImportWorker] Successfully completed import for product ${productId}: ${productName}`);
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error during analysis';
    console.error(`[ProductImportWorker] Failed to process product ${productId}:`, error);

    // Check if product still exists before updating error status
    try {
      const productForErrorUpdate = await context.entities.Product.findUnique({
        where: { id: productId },
      });

      if (productForErrorUpdate) {
        // Update product with error status only if it still exists
        await context.entities.Product.update({
          where: { id: productId },
          data: {
            analysisStatus: 'FAILED',
            analysisError: errorMessage,
            analysisCompletedAt: new Date(),
          },
        });
      } else {
        console.log(`[ProductImportWorker] Product ${productId} was deleted, skipping error status update`);
      }
    } catch (updateError) {
      console.error(`[ProductImportWorker] Failed to update error status for product ${productId}:`, updateError);
    }

    // Re-throw error to trigger pg-boss retry mechanism
    throw error;
  }
};
