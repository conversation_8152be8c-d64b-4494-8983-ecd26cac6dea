/**
 * WebSocket Types
 *
 * This file defines the types for WebSocket events.
 * Note: Canvas collaboration types moved to Cloudflare Workers system
 */

// Define WebSocket event types
export enum WebSocketEventType {
  // Canvas events
  CANVAS_ELEMENT_ADD = 'canvasElementAdd',
  CANVAS_ELEMENT_UPDATE = 'canvasElementUpdate',
  CANVAS_ELEMENT_REMOVE = 'canvasElementRemove',
  CANVAS_ELEMENT_SELECT = 'canvasElementSelect',

  // Task events
  TASK_UPDATE = 'taskUpdate',
  TASK_COMPLETE = 'taskComplete',
  TASK_ERROR = 'taskError',

  // Progress events
  UPDATE_PENDING_IMAGE_PROGRESS = 'updatePendingImageProgress',
  UPDATE_PENDING_NEWSLETTER_PROGRESS = 'updatePendingNewsletterProgress',
  UPDATE_PENDING_GENERIC_PROGRESS = 'updatePendingGenericProgress',

  // HTML streaming events
  HTML_STREAM_UPDATE = 'htmlStreamUpdate',

  // Voice agent events
  VOICE_AGENT_REQUEST = 'voiceAgentRequest',
  VOICE_AGENT_RESPONSE = 'voiceAgentResponse',
  VOICE_AGENT_CANVAS_UPDATE = 'voiceAgentCanvasUpdate',
  VOICE_AGENT_STATUS = 'voiceAgentStatus',
  VOICE_AGENT_TRANSCRIPT = 'voiceAgentTranscript',

  // Chat events
  CHAT_MESSAGE = 'chatMessage',
  CHAT_TOOL_CALL = 'chatToolCall',
  CHAT_TOOL_RESULT = 'chatToolResult',

  // Newsletter Flow Events
  NEWSLETTER_OUTLINE_READY = 'newsletter_outline_ready',
}

// Define voice agent status data
export interface VoiceAgentStatusData {
  roomName: string;
  status: 'disconnected' | 'connecting' | 'connected' | 'listening' | 'processing' | 'speaking' | 'error';
  error?: string;
}

// Define voice agent transcript data
export interface VoiceAgentTranscriptData {
  roomName: string;
  transcript: string;
  isFinal: boolean;
  speaker: 'user' | 'agent';
}

// Define voice agent response data
export interface VoiceAgentResponseData {
  taskId: string;
  roomName: string;
  response: string;
}

// Define voice agent canvas update data
export interface VoiceAgentCanvasUpdateData {
  type: string;
  data: any;
}

// Define task update data
export interface TaskUpdateData {
  taskId: string;
  status: string;
  progress: number;
  result?: any;
  error?: string;
}

// Define task complete data
export interface TaskCompleteData {
  taskId: string;
  result: any;
}

// Define task error data
export interface TaskErrorData {
  taskId: string;
  error: string;
}

// Define pending image progress data
export interface PendingImageProgressData {
  taskId: string;
  status: string;
  progress: number;
  imageUrl?: string;
}

// Define pending newsletter progress data
export interface PendingNewsletterProgressData {
  taskId: string;
  status: string;
  progress: number;
  html?: string;
}

// Define pending generic progress data
export interface PendingGenericProgressData {
  taskId: string;
  status: string;
  progress: number;
  result?: any;
}

// Define HTML stream update data
export interface HtmlStreamUpdateData {
  taskId: string;
  html: string;
  isComplete: boolean;
}

// Define canvas element add data
export interface CanvasElementAddData {
  elementId: string;
  elementType: string;
  position: { x: number; y: number };
  size: { width: number; height: number };
  content: any;
  taskId?: string;
}

// Define canvas element update data
export interface CanvasElementUpdateData {
  elementId: string;
  position?: { x: number; y: number };
  size?: { width: number; height: number };
  content?: any;
}

// Define canvas element remove data
export interface CanvasElementRemoveData {
  elementId: string;
  id?: string; // Added for client compatibility
}

// Define canvas element select data
export interface CanvasElementSelectData {
  elementId: string;
  selected: boolean;
}

// Define chat message data
export interface ChatMessageData {
  chatId: string;
  messageId: string;
  content: string;
  sender: string;
  timestamp: number;
}

// Define chat tool call data
export interface ChatToolCallData {
  chatId: string;
  messageId: string;
  toolName: string;
  args: any;
}

// Define chat tool result data
export interface ChatToolResultData {
  chatId: string;
  messageId: string;
  toolName: string;
  result: any;
}

// Define data for newsletter outline ready event
export interface NewsletterOutlineReadyData {
  taskId: string;
  threadId: string;
  outline: any; // Consider defining a strict type for NewsletterOutline
  clientLoadingCardId?: string;
}

// Data type for updating newsletter card status (e.g., completion, failure)
export interface UpdateNewsletterCardStatusData {
  taskId: string;
  status: string; // e.g., 'completed', 'failed', 'processing_html', 'newsletter_assets_generated'
  result?: any; // Contains final URLs like gmailMockUrl, or error messages
  requestType?: string; // To help client identify it's a newsletter task, e.g., 'generate_newsletter'
}

// Define client to server events
export interface ClientToServerEvents {
  // Authentication
  authenticate: (data: { userId: string }) => void;

  // Agent events
  startAgent: (data: { userId: string; initialMessage: string; metadata?: any }) => void;
  resumeAgent: (data: { threadId: string; humanInput: string; metadata?: any }) => void;
  pauseAgent: (data: { threadId: string; reason?: string }) => void;
  cancelAgent: (data: { threadId: string; reason?: string }) => void;

  // Voice agent events
  startVoiceAgent: (data: { roomName: string; systemPrompt?: string }) => void;
  stopVoiceAgent: (data: { roomName: string }) => void;

  // Canvas events
  addCanvasElement: (data: CanvasElementAddData) => void;
  updateCanvasElement: (data: CanvasElementUpdateData) => void;
  removeCanvasElement: (data: CanvasElementRemoveData) => void;
  selectCanvasElement: (data: CanvasElementSelectData) => void;

  // Note: Canvas collaboration events removed - now handled by Cloudflare Workers
}

// Define server to client events
export interface ServerToClientEvents {
  // Agent events
  agent_started: (data: { threadId: string; message: string }) => void;
  agent_resumed: (data: { threadId: string; message: string }) => void;
  agent_paused: (data: { threadId: string; message: string }) => void;
  agent_cancelled: (data: { threadId: string; message: string }) => void;
  agent_error: (data: { message: string; error?: string }) => void;
  agent_complete: (data: { threadId: string; message: string }) => void;
  update_progress: (data: { threadId: string; message: string; progress: number; error?: boolean }) => void;
  request_human_input: (data: { threadId: string; question: string; context: string; options: any }) => void;

  // Task events
  taskUpdate: (data: TaskUpdateData) => void;
  taskComplete: (data: TaskCompleteData) => void;
  taskError: (data: TaskErrorData) => void;

  // Progress events
  updatePendingImageProgress: (data: PendingImageProgressData) => void;
  updatePendingNewsletterProgress: (data: PendingNewsletterProgressData) => void;
  updatePendingGenericProgress: (data: PendingGenericProgressData) => void;

  // HTML streaming events
  htmlStreamUpdate: (data: HtmlStreamUpdateData) => void;

  // Voice agent events
  voiceAgentStatus: (data: VoiceAgentStatusData) => void;
  voiceAgentTranscript: (data: VoiceAgentTranscriptData) => void;
  voiceAgentResponse: (data: VoiceAgentResponseData) => void;
  voiceAgentCanvasUpdate: (data: VoiceAgentCanvasUpdateData) => void;

  // Chat events
  chatMessage: (data: ChatMessageData) => void;
  chatToolCall: (data: ChatToolCallData) => void;
  chatToolResult: (data: ChatToolResultData) => void;

  // Canvas events
  canvasElementAdd: (data: CanvasElementAddData) => void;
  canvasElementUpdate: (data: CanvasElementUpdateData) => void;
  canvasElementRemove: (data: CanvasElementRemoveData) => void;
  canvasElementSelect: (data: CanvasElementSelectData) => void;

  // Newsletter Flow Events
  newsletter_outline_ready: (data: NewsletterOutlineReadyData) => void;

  // Note: Canvas collaboration events removed - now handled by Cloudflare Workers
}

// Define inter-server events
export interface InterServerEvents {}

// Define socket data
export interface SocketData {
  userId?: string;
  authenticated: boolean;
}

// Define cursor info
export interface CursorInfo {
  position: { x: number; y: number };
  username: string;
  color: string;
  isVisible?: boolean;
}

// Define cursor update payload
export interface CursorUpdatePayload {
  userId: string | null;
  username: string;
  position?: { x: number; y: number };
}

// Define user info
export interface UserInfo {
  userId: string;
  username: string;
  color: string;
}

// Define user joined payload
export type UserJoinedPayload = Omit<UserInfo, 'color'>;

// Define user left payload
export interface UserLeftPayload {
  userId: string;
}

// Define screenshot data
export interface ScreenshotData {
  imageUrl: string;
  taskId: string;
  userId: string;
}
