import { fal } from '@fal-ai/client';

// Configure Fal.ai client
fal.config({
  credentials: process.env.FAL_KEY,
});

export interface FalImageGenerationOptions {
  prompt: string;
  negative_prompt?: string;
  aspect_ratio?: '1:1' | '16:9' | '9:16' | '3:4' | '4:3';
  num_images?: number;
  seed?: number;
}

export interface FalImageGenerationResult {
  success: boolean;
  images?: Array<{
    url: string;
    content_type?: string;
    file_name?: string;
    file_size?: number;
    width?: number;
    height?: number;
  }>;
  seed?: number;
  error?: string;
}

export interface FluxKontextOptions {
  image_url: string;
  prompt: string;
  num_inference_steps?: number;
  seed?: number;
  guidance_scale?: number;
  sync_mode?: boolean;
  num_images?: number;
  enable_safety_checker?: boolean;
  resolution_mode?: 'auto' | 'match_input';
}

export interface FluxKontextResult {
  success: boolean;
  images?: Array<{
    url: string;
    width: number;
    height: number;
    content_type: string;
  }>;
  seed?: number;
  has_nsfw_concepts?: boolean[];
  prompt?: string;
  error?: string;
}

export class FalAiProvider {
  /**
   * Generate images using Fal.ai's Imagen 4 model
   */
  static async generateImage(options: FalImageGenerationOptions): Promise<FalImageGenerationResult> {
    try {
      const result = await fal.subscribe('fal-ai/imagen4/preview', {
        input: {
          prompt: options.prompt,
          negative_prompt: options.negative_prompt || '',
          aspect_ratio: options.aspect_ratio || '1:1',
          num_images: options.num_images || 1,
          ...(options.seed && { seed: options.seed }),
        },
        logs: true,
        onQueueUpdate: (update) => {
          if (update.status === 'IN_PROGRESS') {
            update.logs?.map((log) => log.message).forEach(console.log);
          }
        },
      });

      return {
        success: true,
        images: result.data.images,
        seed: result.data.seed,
      };
    } catch (error) {
      console.error('Fal.ai image generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate image',
      };
    }
  }

  /**
   * Generate character avatar with specific prompt construction
   */
  static async generateCharacterAvatar(audienceData: any, characterOptions: any): Promise<FalImageGenerationResult> {
    const prompt = this.constructCharacterPrompt(audienceData, characterOptions);

    return this.generateImage({
      prompt,
      aspect_ratio: characterOptions.shot === 'headshot' ? '1:1' : '3:4',
      num_images: 1,
    });
  }

  /**
   * Edit images using FLUX Kontext model
   * Accepts multiple images and a prompt for context-aware editing
   */
  static async editImageWithFluxKontext(options: FluxKontextOptions): Promise<FluxKontextResult> {
    try {
      console.log('[FalAi] Starting FLUX Kontext image editing with options:', {
        image_url: options.image_url,
        prompt: options.prompt,
        num_images: options.num_images || 1,
      });

      const result = await fal.subscribe('fal-ai/flux-kontext-lora', {
        input: {
          image_url: options.image_url,
          prompt: options.prompt,
          num_inference_steps: options.num_inference_steps || 30,
          guidance_scale: options.guidance_scale || 2.5,
          num_images: options.num_images || 1,
          enable_safety_checker: options.enable_safety_checker ?? true,
          resolution_mode: options.resolution_mode || 'match_input',
          sync_mode: options.sync_mode || false,
          ...(options.seed && { seed: options.seed }),
        },
        logs: true,
        onQueueUpdate: (update) => {
          if (update.status === 'IN_PROGRESS') {
            console.log('[FalAi] FLUX Kontext progress:', update.status);
            update.logs?.map((log) => log.message).forEach(console.log);
          }
        },
      });

      console.log('[FalAi] FLUX Kontext editing completed successfully');

      return {
        success: true,
        images: result.data.images,
        seed: result.data.seed,
        has_nsfw_concepts: result.data.has_nsfw_concepts,
        prompt: result.data.prompt,
      };
    } catch (error) {
      console.error('[FalAi] FLUX Kontext editing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to edit image with FLUX Kontext',
      };
    }
  }

  /**
   * Edit images with streaming support using FLUX Kontext
   * Useful for real-time progress updates
   */
  static async editImageWithFluxKontextStream(
    options: FluxKontextOptions,
    onProgress?: (event: any) => void
  ): Promise<FluxKontextResult> {
    try {
      console.log('[FalAi] Starting FLUX Kontext streaming with options:', {
        image_url: options.image_url,
        prompt: options.prompt,
        num_images: options.num_images || 1,
      });

      const stream = await fal.stream('fal-ai/flux-kontext-lora', {
        input: {
          image_url: options.image_url,
          prompt: options.prompt,
          num_inference_steps: options.num_inference_steps || 30,
          guidance_scale: options.guidance_scale || 2.5,
          num_images: options.num_images || 1,
          enable_safety_checker: options.enable_safety_checker ?? true,
          resolution_mode: options.resolution_mode || 'match_input',
          ...(options.seed && { seed: options.seed }),
        },
      });

      // Handle streaming events
      for await (const event of stream) {
        console.log('[FalAi] FLUX Kontext stream event:', event);
        onProgress?.(event);
      }

      const result = await stream.done();
      console.log('[FalAi] FLUX Kontext streaming completed successfully');

      return {
        success: true,
        images: result.data.images,
        seed: result.data.seed,
        has_nsfw_concepts: result.data.has_nsfw_concepts,
        prompt: result.data.prompt,
      };
    } catch (error) {
      console.error('[FalAi] FLUX Kontext streaming error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to edit image with FLUX Kontext streaming',
      };
    }
  }

  /**
   * Edit multiple images sequentially using FLUX Kontext
   * Processes each image with the same prompt
   */
  static async editMultipleImagesWithFluxKontext(
    imageUrls: string[],
    prompt: string,
    options?: Partial<FluxKontextOptions>
  ): Promise<FluxKontextResult[]> {
    console.log(`[FalAi] Starting batch FLUX Kontext editing for ${imageUrls.length} images`);

    const results: FluxKontextResult[] = [];

    for (let i = 0; i < imageUrls.length; i++) {
      const imageUrl = imageUrls[i];
      console.log(`[FalAi] Processing image ${i + 1}/${imageUrls.length}: ${imageUrl}`);

      try {
        const result = await this.editImageWithFluxKontext({
          image_url: imageUrl,
          prompt,
          ...options,
        });

        results.push(result);

        if (result.success) {
          console.log(`[FalAi] Successfully processed image ${i + 1}/${imageUrls.length}`);
        } else {
          console.error(`[FalAi] Failed to process image ${i + 1}/${imageUrls.length}:`, result.error);
        }
      } catch (error) {
        console.error(`[FalAi] Error processing image ${i + 1}/${imageUrls.length}:`, error);
        results.push({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }

      // Add a small delay between requests to avoid rate limiting
      if (i < imageUrls.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`[FalAi] Completed batch FLUX Kontext editing. ${results.filter(r => r.success).length}/${imageUrls.length} successful`);
    return results;
  }

  /**
   * Construct a detailed prompt for character generation
   */
  private static constructCharacterPrompt(character: any, options: any): string {
    // If character has a visual description from AI, use it as the base
    if (character.visualDescription) {
      const parts = [
        // Base quality settings
        'professional high-quality photograph,',
        'cinematic lighting, studio setup, high resolution,',

        // Use the AI-generated visual description
        character.visualDescription,

        // Add specific options that override the description
        options.clothing ? `wearing ${options.clothing} clothing,` : '',

        // Pose and composition
        this.getPoseDescription(options.pose),

        // Shot framing
        this.getShotDescription(options.shot),

        // Style and rendering
        this.getStyleDescription(options.style),

        // Environment and background
        'clean professional background, subtle depth of field,',

        // Quality markers
        '8k resolution, professional photography,',
        'sharp details, perfect focus',
      ];

      return parts.filter(Boolean).join(' ');
    }

    // Fallback to the original method if no visual description
    const parts = [
      // Base quality settings
      'professional high-quality photograph,',
      'cinematic lighting, studio setup, high resolution,',

      // Demographics
      options.gender && options.gender !== 'any' ? `${options.gender},` : '',
      this.getAgeDescriptor(options.age || character.age),
      options.ethnicity && options.ethnicity !== 'any' ? `${options.ethnicity} ethnicity,` : '',

      // Physical features
      options.hairColor && options.hairColor !== 'any' ? `${options.hairColor} hair,` : '',
      options.eyeColor && options.eyeColor !== 'any' ? `${options.eyeColor} eyes,` : '',
      options.bodyType && options.bodyType !== 'average' ? `${options.bodyType} build,` : '',

      // Professional context
      character.occupation || character.audience?.occupation
        ? `${character.occupation || character.audience.occupation},`
        : '',

      // Clothing style
      options.clothing ? `${options.clothing} clothing,` : 'professional attire,',

      // Pose and composition
      this.getPoseDescription(options.pose),

      // Shot framing
      this.getShotDescription(options.shot),

      // Style and rendering
      this.getStyleDescription(options.style),

      // Environment and background
      'clean professional background, subtle depth of field,',

      // Quality markers
      '8k resolution, professional photography,',
      'sharp details, perfect focus',
    ];

    return parts.filter(Boolean).join(' ');
  }

  private static getAgeDescriptor(age?: string): string {
    if (!age) return 'professional adult,';

    // Handle age ranges
    if (age.includes('-')) {
      const [min] = age.split('-').map((n) => parseInt(n.trim()));
      if (min <= 25) return 'young professional, fresh and energetic look,';
      if (min >= 45) return 'experienced professional, confident and distinguished look,';
      return 'established professional, confident and experienced look,';
    }

    return 'professional adult,';
  }

  private static getPoseDescription(pose?: string): string {
    switch (pose) {
      case 'sitting':
        return 'sitting pose, relaxed yet professional,';
      case 'walking':
        return 'walking pose, dynamic and confident,';
      case 'portrait':
        return 'portrait pose, direct eye contact,';
      case 'action':
        return 'action pose, dynamic and engaging,';
      default:
        return 'standing confidently, professional posture,';
    }
  }

  private static getShotDescription(shot?: string): string {
    switch (shot) {
      case 'headshot':
        return 'close-up headshot, shoulders up,';
      case 'half-body':
        return 'medium shot, waist up,';
      default:
        return 'full body shot, professional composition,';
    }
  }

  private static getStyleDescription(style?: string): string {
    switch (style) {
      case 'artistic':
        return 'stylized character design, modern illustration style,';
      case 'cartoon':
        return 'cartoon style, friendly and approachable,';
      default:
        return 'photorealistic, natural skin texture, detailed features,';
    }
  }
}
