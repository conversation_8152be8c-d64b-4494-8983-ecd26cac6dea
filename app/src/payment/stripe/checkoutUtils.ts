import type { StripeMode } from './paymentProcessor';
import <PERSON><PERSON> from 'stripe';
import { stripe } from './stripeClient';

// WASP_WEB_CLIENT_URL will be set up by Wasp when deploying to production: https://wasp-lang.dev/docs/deploying
const DOMAIN = process.env.WASP_WEB_CLIENT_URL || 'http://localhost:3000';

export async function fetchStripeCustomer(customerEmail: string) {
  let customer: Stripe.Customer;
  try {
    const stripeCustomers = await stripe.customers.list({
      email: customerEmail,
    });
    if (!stripeCustomers.data.length) {
      console.log('creating customer');
      customer = await stripe.customers.create({
        email: customerEmail,
      });
    } else {
      console.log('using existing customer');
      customer = stripeCustomers.data[0];
    }
    return customer;
  } catch (error) {
    console.error('fetchStripeCustomer: ', error);
    throw error;
  }
}

export async function createStripeCheckoutSession({
  priceId,
  customerId,
  mode,
}: {
  userId: string;
  priceId: string;
  customerId: string;
  mode: StripeMode;
}) {
  try {
    const payload: any = {
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: mode,
      success_url: `${DOMAIN}/checkout?success=true`,
      cancel_url: `${DOMAIN}/checkout?canceled=true`,
      automatic_tax: { enabled: true },
      customer_update: {
        address: 'auto',
      },
      customer: customerId,
      saved_payment_method_options: {
        payment_method_save: 'enabled',
      },
    };

    return await stripe.checkout.sessions.create(payload);
  } catch (error) {
    console.error(error);
    throw error;
  }
}
